import React from 'react';
import { motion } from 'framer-motion';

interface AIIntroductionProps {
  selectedRole: 'CLIENT' | 'EXPERT';
  onStartConversation: () => void;
  isLoading: boolean;
  onBack: () => void;
}

interface ExpectationItemProps {
  text: string;
  icon: string;
}

const ExpectationItem: React.FC<ExpectationItemProps> = ({ text, icon }) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.4 }}
      className="flex items-center space-x-3 rtl:space-x-reverse p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm"
    >
      <div className="text-2xl">{icon}</div>
      <span className="text-gray-700 dark:text-gray-300">{text}</span>
    </motion.div>
  );
};

const AIIntroduction: React.FC<AIIntroductionProps> = ({
  selectedRole,
  onStartConversation,
  isLoading,
  onBack
}) => {
  const isExpert = selectedRole === 'EXPERT';

  const expertExpectations = [
    { text: "أسئلة حول مهاراتك وخبراتك المهنية", icon: "💼" },
    { text: "تحليل أعمالك السابقة ومشاريعك", icon: "📊" },
    { text: "اقتراحات لتحسين ملفك الشخصي", icon: "✨" },
    { text: "تحديد أسعارك المناسبة للسوق", icon: "💰" },
    { text: "نصائح لجذب العملاء المناسبين", icon: "🎯" }
  ];

  const clientExpectations = [
    { text: "أسئلة حول مشاريعك واحتياجاتك", icon: "📋" },
    { text: "تحديد نوع الخبراء المطلوبين", icon: "👥" },
    { text: "اقتراحات لميزانيتك وجدولك الزمني", icon: "⏰" },
    { text: "نصائح للتعامل مع الخبراء", icon: "🤝" },
    { text: "إرشادات لكتابة مشاريع فعالة", icon: "📝" }
  ];

  const expectations = isExpert ? expertExpectations : clientExpectations;

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-12"
      >
        {/* AI Avatar */}
        <div className="relative inline-block mb-6">
          <div className="w-24 h-24 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center shadow-lg">
            <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          
          {/* Animated pulse rings */}
          <div className="absolute inset-0 rounded-full border-2 border-primary-300 animate-ping opacity-20"></div>
          <div className="absolute inset-0 rounded-full border-2 border-primary-400 animate-ping opacity-30" style={{ animationDelay: '0.5s' }}></div>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          {isExpert ? 'مساعد الخبراء الذكي' : 'مساعد العملاء الذكي'}
        </h1>
        
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto leading-relaxed">
          {isExpert 
            ? 'سأساعدك في إعداد ملفك المهني وتحديد مهاراتك وخدماتك بطريقة تفاعلية وذكية'
            : 'سأساعدك في تحديد احتياجاتك ومتطلبات مشاريعك للعثور على أفضل الخبراء'
          }
        </p>
      </motion.div>

      {/* What to Expect Section */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="bg-gradient-to-br from-gray-50 to-white dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8 mb-8 shadow-lg backdrop-blur-sm"
      >
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
          ماذا تتوقع من هذه المحادثة؟
        </h2>
        
        <div className="grid gap-4 max-w-2xl mx-auto">
          {expectations.map((expectation, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.1 * index }}
            >
              <ExpectationItem 
                text={expectation.text} 
                icon={expectation.icon}
              />
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Time Estimate */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.4, delay: 0.4 }}
        className="text-center mb-8"
      >
        <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-full">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-sm font-medium">
            الوقت المتوقع: 5-10 دقائق
          </span>
        </div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.6 }}
        className="flex flex-col sm:flex-row gap-4 justify-center items-center"
      >
        <button
          onClick={onStartConversation}
          disabled={isLoading}
          className="
            px-8 py-4 bg-gradient-to-r from-primary-600 to-primary-700 
            text-white font-semibold rounded-xl
            hover:from-primary-700 hover:to-primary-800
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-all duration-300 transform hover:scale-105
            shadow-lg hover:shadow-xl
            backdrop-blur-sm
            min-w-[200px]
          "
        >
          {isLoading ? (
            <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              <span>جاري البدء...</span>
            </div>
          ) : (
            <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
              <span>ابدأ المحادثة الآن</span>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </div>
          )}
        </button>

        <button
          onClick={onBack}
          disabled={isLoading}
          className="
            px-6 py-3 bg-gray-100 dark:bg-gray-700 
            text-gray-700 dark:text-gray-300 font-medium rounded-xl
            hover:bg-gray-200 dark:hover:bg-gray-600
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-all duration-300
            min-w-[120px]
          "
        >
          العودة
        </button>
      </motion.div>

      {/* Privacy Notice */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.4, delay: 0.8 }}
        className="mt-8 text-center"
      >
        <p className="text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto">
          🔒 جميع المعلومات التي تشاركها معنا محمية ومشفرة. نحن نحترم خصوصيتك ولن نشارك بياناتك مع أطراف ثالثة.
        </p>
      </motion.div>
    </div>
  );
};

export default AIIntroduction;
