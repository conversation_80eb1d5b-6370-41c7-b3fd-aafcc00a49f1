import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

interface StartConversationRequest {
  userRole: 'CLIENT' | 'EXPERT';
  language: 'ar' | 'en';
  sessionType: 'onboarding' | 'profile_optimization' | 'service_creation';
  culturalContext?: {
    location?: string;
    dialect?: string;
  };
}

interface StartConversationResponse {
  success: boolean;
  message: string;
  data?: {
    sessionId: string;
    currentStep: string;
    messages: any[];
    extractedData: any;
    status: string;
    completionRate: number;
  };
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<StartConversationResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Get user session
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.id) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const { userRole, language, sessionType, culturalContext }: StartConversationRequest = req.body;

    // Validate required fields
    if (!userRole || !language) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: userRole, language'
      });
    }

    // Forward request to the main API
    const apiResponse = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3000'}/api/v1/ai/conversation/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.user.id}`, // Use user ID as token for now
      },
      body: JSON.stringify({
        userRole,
        language,
        sessionType: sessionType || 'onboarding',
        culturalContext: culturalContext || {
          location: 'سوريا',
          dialect: 'general'
        }
      }),
    });

    if (!apiResponse.ok) {
      const errorData = await apiResponse.json().catch(() => ({}));
      throw new Error(errorData.message || `API request failed with status ${apiResponse.status}`);
    }

    const apiData = await apiResponse.json();

    if (!apiData.success) {
      throw new Error(apiData.message || 'Failed to start conversation');
    }

    // Generate welcome message based on role and language
    const welcomeMessage = generateWelcomeMessage(userRole, language);

    // Return successful response
    return res.status(201).json({
      success: true,
      message: 'AI conversation started successfully',
      data: {
        sessionId: apiData.data.sessionId,
        currentStep: apiData.data.currentStep || 'welcome',
        messages: [
          {
            id: `welcome_${Date.now()}`,
            role: 'assistant',
            content: welcomeMessage,
            timestamp: new Date().toISOString(),
            confidence: 1.0
          }
        ],
        extractedData: apiData.data.extractedData || {},
        status: apiData.data.status || 'active',
        completionRate: 0.0
      }
    });

  } catch (error: any) {
    console.error('Error starting AI conversation:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}

/**
 * Generate welcome message based on user role and language
 */
function generateWelcomeMessage(userRole: 'CLIENT' | 'EXPERT', language: 'ar' | 'en'): string {
  if (language === 'ar') {
    if (userRole === 'EXPERT') {
      return `مرحباً بك! أنا مساعدك الذكي في منصة فريلا سوريا. 

سأساعدك في إعداد ملفك المهني بطريقة تفاعلية وذكية. سنتحدث عن:
• مهاراتك وخبراتك المهنية
• الخدمات التي تريد تقديمها
• أسعارك المناسبة للسوق السوري
• كيفية جذب العملاء المناسبين

لنبدأ! ما هو مجال تخصصك الرئيسي؟`;
    } else {
      return `مرحباً بك! أنا مساعدك الذكي في منصة فريلا سوريا.

سأساعدك في تحديد احتياجاتك وإيجاد أفضل الخبراء لمشاريعك. سنتحدث عن:
• نوع المشاريع التي تحتاج إنجازها
• الميزانية والجدول الزمني المناسب
• المهارات المطلوبة في الخبراء
• كيفية التواصل الفعال مع الخبراء

لنبدأ! ما نوع المشروع الذي تفكر في تنفيذه؟`;
    }
  } else {
    if (userRole === 'EXPERT') {
      return `Welcome! I'm your AI assistant on Freela Syria platform.

I'll help you set up your professional profile interactively. We'll discuss:
• Your skills and professional experience
• Services you want to offer
• Appropriate pricing for the Syrian market
• How to attract the right clients

Let's start! What is your main area of expertise?`;
    } else {
      return `Welcome! I'm your AI assistant on Freela Syria platform.

I'll help you identify your needs and find the best experts for your projects. We'll discuss:
• Types of projects you need to complete
• Appropriate budget and timeline
• Required skills in experts
• How to communicate effectively with experts

Let's start! What type of project are you thinking of implementing?`;
    }
  }
}
