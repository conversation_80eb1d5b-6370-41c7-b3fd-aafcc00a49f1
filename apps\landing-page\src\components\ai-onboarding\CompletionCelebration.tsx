import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface CompletionCelebrationProps {
  selectedRole: 'CLIENT' | 'EXPERT';
  extractedData?: any;
}

const CompletionCelebration: React.FC<CompletionCelebrationProps> = ({
  selectedRole,
  extractedData
}) => {
  const [showConfetti, setShowConfetti] = useState(true);
  const isExpert = selectedRole === 'EXPERT';

  useEffect(() => {
    // Hide confetti after 3 seconds
    const timer = setTimeout(() => {
      setShowConfetti(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  // Confetti animation
  const confettiColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
  const confettiPieces = Array.from({ length: 50 }, (_, i) => ({
    id: i,
    color: confettiColors[Math.floor(Math.random() * confettiColors.length)],
    delay: Math.random() * 2,
    duration: 3 + Math.random() * 2,
    x: Math.random() * 100,
    rotation: Math.random() * 360,
  }));

  return (
    <div className="max-w-4xl mx-auto text-center relative overflow-hidden">
      {/* Confetti Animation */}
      {showConfetti && (
        <div className="absolute inset-0 pointer-events-none">
          {confettiPieces.map((piece) => (
            <motion.div
              key={piece.id}
              initial={{
                y: -20,
                x: `${piece.x}%`,
                rotate: 0,
                opacity: 1,
              }}
              animate={{
                y: '100vh',
                rotate: piece.rotation,
                opacity: 0,
              }}
              transition={{
                duration: piece.duration,
                delay: piece.delay,
                ease: 'easeOut',
              }}
              className="absolute w-3 h-3 rounded-sm"
              style={{ backgroundColor: piece.color }}
            />
          ))}
        </div>
      )}

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6, type: 'spring', bounce: 0.4 }}
        className="relative z-10"
      >
        {/* Success Icon */}
        <div className="mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2, type: 'spring', bounce: 0.6 }}
            className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full shadow-lg"
          >
            <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </motion.div>
        </div>

        {/* Celebration Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            🎉 تهانينا! 🎉
          </h1>
          
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto leading-relaxed">
            {isExpert 
              ? 'تم إعداد ملفك المهني بنجاح! أنت الآن جاهز لبدء رحلتك كخبير في منصة فريلا سوريا'
              : 'تم إعداد حسابك بنجاح! أنت الآن جاهز لاستكشاف الخبراء والبدء في مشاريعك'
            }
          </p>
        </motion.div>

        {/* Summary Cards */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="grid md:grid-cols-3 gap-6 mb-8"
        >
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
            <div className="text-3xl mb-3">
              {isExpert ? '💼' : '🎯'}
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              {isExpert ? 'ملف مهني متكامل' : 'أهداف واضحة'}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {isExpert 
                ? 'تم إنشاء ملفك المهني بناءً على خبراتك ومهاراتك'
                : 'تم تحديد احتياجاتك ومتطلبات مشاريعك'
              }
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
            <div className="text-3xl mb-3">🤖</div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              مساعد ذكي
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              سيواصل المساعد الذكي دعمك وتقديم التوصيات المناسبة
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
            <div className="text-3xl mb-3">🚀</div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              جاهز للانطلاق
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {isExpert 
                ? 'يمكنك الآن إنشاء خدماتك واستقبال طلبات العملاء'
                : 'يمكنك الآن تصفح الخبراء وإنشاء مشاريعك'
              }
            </p>
          </div>
        </motion.div>

        {/* Next Steps */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 p-6 rounded-xl mb-8"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            الخطوات التالية:
          </h3>
          
          <div className="grid md:grid-cols-2 gap-4 text-left rtl:text-right">
            {isExpert ? (
              <>
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <div className="w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold mt-0.5">1</div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">أكمل ملفك الشخصي</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">أضف المزيد من التفاصيل والأعمال السابقة</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <div className="w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold mt-0.5">2</div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">أنشئ خدماتك</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">قم بإنشاء خدماتك الأولى لجذب العملاء</p>
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <div className="w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold mt-0.5">1</div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">استكشف الخبراء</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">تصفح الخبراء واعثر على المناسب لمشروعك</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <div className="w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold mt-0.5">2</div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">انشر مشروعك</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">أنشئ مشروعك الأول واستقبل العروض</p>
                  </div>
                </div>
              </>
            )}
          </div>
        </motion.div>

        {/* Redirect Message */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="text-center"
        >
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-full">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm">
              {isExpert 
                ? 'جاري توجيهك إلى لوحة تحكم الخبراء...'
                : 'جاري توجيهك إلى الصفحة الرئيسية...'
              }
            </span>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default CompletionCelebration;
