/**
 * AI Chat Interface for Web-based AI Onboarding
 * Connects to Phase 3 AI services for intelligent profile setup
 */

import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'next-i18next';
import { motion, AnimatePresence } from 'framer-motion';

// Types
interface AIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  confidence?: number;
  extractedData?: any;
}

interface AIChatInterfaceProps {
  userRole: 'CLIENT' | 'EXPERT';
  language: string;
  onComplete: (extractedData: any) => void;
  onError: (error: string) => void;
}

export const AIChatInterface: React.FC<AIChatInterfaceProps> = ({
  userRole,
  language,
  onComplete,
  onError
}) => {
  const { t } = useTranslation(['ai-onboarding', 'common']);
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState('welcome');
  const [completionRate, setCompletionRate] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize AI conversation
  useEffect(() => {
    initializeConversation();
  }, []);

  const initializeConversation = async () => {
    try {
      setIsLoading(true);
      
      // Start AI conversation with backend
      const response = await fetch('/api/ai/conversation/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userRole,
          language,
          sessionType: 'onboarding',
          culturalContext: {
            location: 'سوريا',
            dialect: 'general'
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to start AI conversation');
      }

      const data = await response.json();
      setSessionId(data.sessionId);
      
      // Add welcome message
      const welcomeMessage: AIMessage = {
        id: `ai_${Date.now()}`,
        role: 'assistant',
        content: data.welcomeMessage || getWelcomeMessage(),
        timestamp: new Date()
      };
      
      setMessages([welcomeMessage]);
    } catch (error) {
      console.error('Failed to initialize AI conversation:', error);
      onError('فشل في بدء المحادثة الذكية');
    } finally {
      setIsLoading(false);
    }
  };

  const getWelcomeMessage = () => {
    if (userRole === 'EXPERT') {
      return language === 'ar' 
        ? 'مرحباً! أنا مساعدك الذكي لإعداد ملفك المهني في فريلا سوريا. سأساعدك في تحديد مهاراتك وخدماتك بطريقة تفاعلية. لنبدأ - ما هو مجال خبرتك الرئيسي؟'
        : 'Hello! I\'m your AI assistant for setting up your professional profile on Freela Syria. I\'ll help you identify your skills and services interactively. Let\'s start - what is your main area of expertise?';
    } else {
      return language === 'ar'
        ? 'مرحباً! أنا مساعدك الذكي في فريلا سوريا. سأساعدك في تحديد احتياجاتك ومتطلبات مشاريعك للعثور على أفضل الخبراء. أخبرني، ما نوع المشروع الذي تعمل عليه؟'
        : 'Hello! I\'m your AI assistant at Freela Syria. I\'ll help you identify your needs and project requirements to find the best experts. Tell me, what type of project are you working on?';
    }
  };

  const sendMessage = async () => {
    if (!inputValue.trim() || !sessionId || isLoading) return;

    const userMessage: AIMessage = {
      id: `user_${Date.now()}`,
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/ai/conversation/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          message: userMessage.content,
          messageType: 'text'
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const data = await response.json();
      
      const aiMessage: AIMessage = {
        id: `ai_${Date.now()}`,
        role: 'assistant',
        content: data.response,
        timestamp: new Date(),
        confidence: data.confidence,
        extractedData: data.extractedData
      };

      setMessages(prev => [...prev, aiMessage]);
      setCurrentStep(data.currentStep || currentStep);
      setCompletionRate(data.completionRate || completionRate);

      // Check if onboarding is complete
      if (data.isCompleted) {
        setTimeout(() => {
          onComplete(data.extractedData);
        }, 2000);
      }

    } catch (error) {
      console.error('Failed to send message:', error);
      onError('فشل في إرسال الرسالة');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Progress Bar */}
      <div className="w-full bg-gray-800/50 backdrop-blur-sm border-b border-gray-700/50">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-300">
              {language === 'ar' ? 'تقدم الإعداد' : 'Setup Progress'}
            </span>
            <span className="text-sm text-gray-400">{Math.round(completionRate)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <motion.div
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${completionRate}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                    : 'bg-gray-800/80 backdrop-blur-sm text-gray-100 border border-gray-700/50'
                }`}
              >
                <p className="text-sm leading-relaxed">{message.content}</p>
                {message.confidence && (
                  <div className="mt-2 text-xs opacity-70">
                    {language === 'ar' ? 'الثقة' : 'Confidence'}: {Math.round(message.confidence * 100)}%
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
        
        {/* Typing Indicator */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex justify-start"
          >
            <div className="bg-gray-800/80 backdrop-blur-sm border border-gray-700/50 rounded-2xl px-4 py-3">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
              </div>
            </div>
          </motion.div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="border-t border-gray-700/50 bg-gray-800/50 backdrop-blur-sm p-6">
        <div className="flex space-x-4 rtl:space-x-reverse">
          <div className="flex-1">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={
                language === 'ar' 
                  ? 'اكتب رسالتك هنا...' 
                  : 'Type your message here...'
              }
              className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent resize-none"
              rows={1}
              disabled={isLoading}
            />
          </div>
          <button
            onClick={sendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            {language === 'ar' ? 'إرسال' : 'Send'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AIChatInterface;
