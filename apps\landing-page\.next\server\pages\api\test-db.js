"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/test-db";
exports.ids = ["pages/api/test-db"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("async_hooks");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("fs/promises");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-db&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-db.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-db&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-db.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_test_db_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\test-db.ts */ \"(api)/./src/pages/api/test-db.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_test_db_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_test_db_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/test-db\",\n        pathname: \"/api/test-db\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_test_db_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-db&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-db.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/test-db.ts":
/*!**********************************!*\
  !*** ./src/pages/api/test-db.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _freela_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @freela/database */ \"(api)/../../packages/database/dist/index.js\");\n/* harmony import */ var _freela_database__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_freela_database__WEBPACK_IMPORTED_MODULE_0__);\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        console.log(\"\\uD83D\\uDD0D Testing database connection...\");\n        // Test database connection\n        const userCount = await _freela_database__WEBPACK_IMPORTED_MODULE_0__.prisma.user.count();\n        console.log(\"✅ Database connection successful, user count:\", userCount);\n        // Test if we can find any users\n        const users = await _freela_database__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findMany({\n            take: 5,\n            select: {\n                id: true,\n                email: true,\n                role: true,\n                hasCompletedOnboarding: true,\n                createdAt: true\n            }\n        });\n        console.log(\"\\uD83D\\uDCCA Sample users:\", users);\n        return res.status(200).json({\n            success: true,\n            message: \"Database connection successful\",\n            data: {\n                userCount,\n                sampleUsers: users\n            }\n        });\n    } catch (error) {\n        console.error(\"❌ Database connection failed:\", error);\n        return res.status(500).json({\n            success: false,\n            error: \"Database connection failed\",\n            details: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/test-db.ts\n");

/***/ }),

/***/ "(api)/../../packages/database/dist/client.js":
/*!**********************************************!*\
  !*** ../../packages/database/dist/client.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PrismaClient = exports.Prisma = exports.getPrismaInstance = exports.withTransaction = exports.checkDatabaseHealth = exports.disconnectDatabase = exports.connectDatabase = exports.prisma = void 0;\nconst client_1 = __webpack_require__(/*! @prisma/client */ \"(api)/../../node_modules/@prisma/client/default.js\");\nObject.defineProperty(exports, \"PrismaClient\", ({ enumerable: true, get: function () { return client_1.PrismaClient; } }));\nObject.defineProperty(exports, \"Prisma\", ({ enumerable: true, get: function () { return client_1.Prisma; } }));\nlet prisma;\n// This is a singleton pattern to ensure we only have one instance of Prisma Client.\nconst getPrismaInstance = () => {\n    if (prisma) {\n        return prisma;\n    }\n    const newPrismaInstance = new client_1.PrismaClient({\n        log:  true ? ['query', 'error', 'warn'] : 0,\n        errorFormat: 'pretty',\n    });\n    // In development, use a global variable to preserve the client across hot reloads.\n    if (true) {\n        if (!globalThis.__prisma) {\n            globalThis.__prisma = newPrismaInstance;\n        }\n        prisma = globalThis.__prisma;\n    }\n    else {}\n    return prisma; // Non-null assertion since we just created it\n};\nexports.getPrismaInstance = getPrismaInstance;\n// Immediately get the instance to be used by the app\nconst prismaInstance = getPrismaInstance();\nexports.prisma = prismaInstance;\n// Graceful shutdown logic\nconst setupGracefulShutdown = (client) => {\n    let isShuttingDown = false;\n    const shutdown = async (signal) => {\n        if (isShuttingDown)\n            return;\n        isShuttingDown = true;\n        console.log(`Received ${signal}. Disconnecting database...`);\n        await client.$disconnect();\n        console.log('Database disconnected.');\n        process.exit(0);\n    };\n    process.on('SIGINT', () => shutdown('SIGINT'));\n    process.on('SIGTERM', () => shutdown('SIGTERM'));\n};\nsetupGracefulShutdown(prismaInstance);\n// Database connection utilities with timeout and graceful failure\nconst connectDatabase = async () => {\n    const client = getPrismaInstance();\n    try {\n        // Race connection against a timeout\n        await Promise.race([\n            client.$connect(),\n            new Promise((_, reject) => setTimeout(() => reject(new Error('Database connection timed out after 10 seconds')), 10000)),\n        ]);\n        console.log('✅ Database connected successfully');\n    }\n    catch (error) {\n        if (error instanceof Error) {\n            console.error('❌ Database connection failed:', error.message);\n        }\n        else {\n            console.error('❌ An unexpected error occurred during database connection:', error);\n        }\n        console.warn('⚠️ Server is starting without a database connection. Some features will be unavailable.');\n        // Do not re-throw; allow the application to start in a degraded state.\n        return; // Explicitly return to avoid any potential re-throw\n    }\n};\nexports.connectDatabase = connectDatabase;\nconst disconnectDatabase = async () => {\n    const client = getPrismaInstance();\n    try {\n        await client.$disconnect();\n        console.log('✅ Database disconnected successfully');\n    }\n    catch (error) {\n        if (error instanceof Error) {\n            console.error('❌ Database disconnection failed:', error.message);\n        }\n        else {\n            console.error('❌ An unexpected error occurred during database disconnection:', error);\n        }\n        // In a disconnect scenario, we should probably throw to indicate a problem.\n        throw error;\n    }\n};\nexports.disconnectDatabase = disconnectDatabase;\n// Health check for the database connection\nconst checkDatabaseHealth = async () => {\n    const client = getPrismaInstance();\n    try {\n        await client.$queryRaw `SELECT 1`;\n        return true;\n    }\n    catch (error) {\n        // Don't spam logs on health checks\n        return false;\n    }\n};\nexports.checkDatabaseHealth = checkDatabaseHealth;\n/**\n * Wrapper for Prisma transactions.\n * @template T\n * @param {(tx: any) => Promise<T>} callback\n * @returns {Promise<T>}\n */\nconst withTransaction = async (callback) => {\n    const client = getPrismaInstance();\n    return await client.$transaction(callback);\n};\nexports.withTransaction = withTransaction;\nexports[\"default\"] = prismaInstance;\n//# sourceMappingURL=client.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../packages/database/dist/client.js\n");

/***/ }),

/***/ "(api)/../../packages/database/dist/index.js":
/*!*********************************************!*\
  !*** ../../packages/database/dist/index.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n// Re-export all database utilities and clients.\n// This acts as the main entry point for the package.\n__exportStar(__webpack_require__(/*! ./client */ \"(api)/../../packages/database/dist/client.js\"), exports);\n__exportStar(__webpack_require__(/*! ./supabase */ \"(api)/../../packages/database/dist/supabase.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/supabase */ \"(api)/../../packages/database/dist/types/supabase.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../packages/database/dist/index.js\n");

/***/ }),

/***/ "(api)/../../packages/database/dist/supabase.js":
/*!************************************************!*\
  !*** ../../packages/database/dist/supabase.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Supabase Client Configuration for Freela Syria\n// This replaces the Prisma client with Supabase client\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SupabaseAIService = exports.SupabaseExpertService = exports.SupabaseUserService = exports.SupabaseAIChat = exports.supabaseAdmin = exports.supabase = void 0;\nconst supabase_js_1 = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n// Supabase configuration\nconst supabaseUrl = process.env.SUPABASE_URL || 'https://bivignfixaqrmdcbsnqh.supabase.co';\nconst supabaseAnonKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpdmlnbmZpeGFxcm1kY2JzbnFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MzY1MDYsImV4cCI6MjA2NTQxMjUwNn0.cMwSd8oFF5CDyXBaaqPL7EVHhF9l32ERd6krX4DAo4E';\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpdmlnbmZpeGFxcm1kY2JzbnFoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTgzNjUwNiwiZXhwIjoyMDY1NDEyNTA2fQ.Ue6KVdG7c-iwZWKr4D-BhRzj82yp2b81uikFYXSdvZ8';\n// Create Supabase clients\nexports.supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    },\n    realtime: {\n        params: {\n            eventsPerSecond: 10\n        }\n    }\n});\n// Service role client for admin operations (server-side only)\nexports.supabaseAdmin = (0, supabase_js_1.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Real-time AI chat subscription helper\nclass SupabaseAIChat {\n    sessionId;\n    subscription;\n    constructor(sessionId) {\n        this.sessionId = sessionId;\n    }\n    // Send a new AI message\n    async sendMessage(content, role, messageType = 'text') {\n        const { data, error } = await exports.supabase\n            .from('ai_conversation_messages')\n            .insert({\n            session_id: this.sessionId,\n            role,\n            content,\n            message_type: messageType,\n            created_at: new Date().toISOString()\n        })\n            .select()\n            .single();\n        if (error) {\n            console.error('Error sending AI message:', error);\n            throw error;\n        }\n        return data;\n    }\n    // Subscribe to real-time messages\n    subscribeToMessages(callback) {\n        this.subscription = exports.supabase\n            .channel(`ai-chat-${this.sessionId}`)\n            .on('postgres_changes', {\n            event: 'INSERT',\n            schema: 'public',\n            table: 'ai_conversation_messages',\n            filter: `session_id=eq.${this.sessionId}`\n        }, (payload) => {\n            callback(payload.new);\n        })\n            .subscribe();\n        return this.subscription;\n    }\n    // Unsubscribe from messages\n    unsubscribe() {\n        if (this.subscription) {\n            exports.supabase.removeChannel(this.subscription);\n        }\n    }\n    // Get conversation history\n    async getMessages(limit = 50) {\n        const { data, error } = await exports.supabase\n            .from('ai_conversation_messages')\n            .select('*')\n            .eq('session_id', this.sessionId)\n            .order('created_at', { ascending: true })\n            .limit(limit);\n        if (error) {\n            console.error('Error fetching messages:', error);\n            throw error;\n        }\n        return data;\n    }\n    // Update session status\n    async updateSession(updates) {\n        const { data, error } = await exports.supabase\n            .from('ai_conversation_sessions')\n            .update({\n            ...updates,\n            last_active_at: new Date().toISOString()\n        })\n            .eq('id', this.sessionId)\n            .select()\n            .single();\n        if (error) {\n            console.error('Error updating session:', error);\n            throw error;\n        }\n        return data;\n    }\n}\nexports.SupabaseAIChat = SupabaseAIChat;\n// User management helpers\nclass SupabaseUserService {\n    // Create a new user with profile\n    static async createUser(userData) {\n        // Sign up the user\n        const { data: authData, error: authError } = await exports.supabase.auth.signUp({\n            email: userData.email,\n            password: userData.password,\n            options: {\n                data: {\n                    first_name: userData.firstName,\n                    last_name: userData.lastName,\n                    role: userData.role,\n                    language: userData.language || 'ar'\n                }\n            }\n        });\n        if (authError) {\n            console.error('Error creating user:', authError);\n            throw authError;\n        }\n        return authData;\n    }\n    // Sign in with Google OAuth\n    static async signInWithGoogle() {\n        const { data, error } = await exports.supabase.auth.signInWithOAuth({\n            provider: 'google',\n            options: {\n                redirectTo: `${window.location.origin}/auth/callback`\n            }\n        });\n        if (error) {\n            console.error('Error signing in with Google:', error);\n            throw error;\n        }\n        return data;\n    }\n    // Get current user profile\n    static async getCurrentUserProfile() {\n        const { data: { user } } = await exports.supabase.auth.getUser();\n        if (!user)\n            return null;\n        const { data: profile, error } = await exports.supabase\n            .from('users')\n            .select(`\n        *,\n        expert_profiles(*),\n        client_profiles(*)\n      `)\n            .eq('id', user.id)\n            .single();\n        if (error) {\n            console.error('Error fetching user profile:', error);\n            throw error;\n        }\n        return profile;\n    }\n    // Update user profile\n    static async updateUserProfile(userId, updates) {\n        const { data, error } = await exports.supabase\n            .from('users')\n            .update(updates)\n            .eq('id', userId)\n            .select()\n            .single();\n        if (error) {\n            console.error('Error updating user profile:', error);\n            throw error;\n        }\n        return data;\n    }\n}\nexports.SupabaseUserService = SupabaseUserService;\n// Expert profile helpers\nclass SupabaseExpertService {\n    // Get expert profile with services\n    static async getExpertProfile(userId) {\n        const { data, error } = await exports.supabase\n            .from('expert_profiles')\n            .select(`\n        *,\n        users(*),\n        services(*)\n      `)\n            .eq('user_id', userId)\n            .single();\n        if (error) {\n            console.error('Error fetching expert profile:', error);\n            throw error;\n        }\n        return data;\n    }\n    // Update expert profile\n    static async updateExpertProfile(userId, updates) {\n        const { data, error } = await exports.supabase\n            .from('expert_profiles')\n            .update(updates)\n            .eq('user_id', userId)\n            .select()\n            .single();\n        if (error) {\n            console.error('Error updating expert profile:', error);\n            throw error;\n        }\n        return data;\n    }\n    // Create a new service\n    static async createService(expertId, serviceData) {\n        const { data, error } = await exports.supabase\n            .from('services')\n            .insert({\n            expert_id: expertId,\n            ...serviceData\n        })\n            .select()\n            .single();\n        if (error) {\n            console.error('Error creating service:', error);\n            throw error;\n        }\n        return data;\n    }\n}\nexports.SupabaseExpertService = SupabaseExpertService;\n// AI onboarding helpers\nclass SupabaseAIService {\n    // Start a new AI conversation session\n    static async startConversation(userId, sessionType, userRole) {\n        const { data, error } = await exports.supabase\n            .from('ai_conversation_sessions')\n            .insert({\n            user_id: userId,\n            session_type: sessionType,\n            user_role: userRole,\n            current_step: 'welcome',\n            status: 'active'\n        })\n            .select()\n            .single();\n        if (error) {\n            console.error('Error starting AI conversation:', error);\n            throw error;\n        }\n        return data;\n    }\n    // Get user's AI sessions\n    static async getUserSessions(userId) {\n        const { data, error } = await exports.supabase\n            .from('ai_conversation_sessions')\n            .select('*')\n            .eq('user_id', userId)\n            .order('created_at', { ascending: false });\n        if (error) {\n            console.error('Error fetching AI sessions:', error);\n            throw error;\n        }\n        return data;\n    }\n    // Create AI recommendation\n    static async createRecommendation(recommendationData) {\n        const { data, error } = await exports.supabase\n            .from('ai_recommendations')\n            .insert(recommendationData)\n            .select()\n            .single();\n        if (error) {\n            console.error('Error creating AI recommendation:', error);\n            throw error;\n        }\n        return data;\n    }\n    // Get user recommendations\n    static async getUserRecommendations(userId) {\n        const { data, error } = await exports.supabase\n            .from('ai_recommendations')\n            .select('*')\n            .eq('user_id', userId)\n            .eq('status', 'pending')\n            .order('priority', { ascending: false });\n        if (error) {\n            console.error('Error fetching recommendations:', error);\n            throw error;\n        }\n        return data;\n    }\n}\nexports.SupabaseAIService = SupabaseAIService;\nexports[\"default\"] = exports.supabase;\n//# sourceMappingURL=supabase.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../packages/database/dist/supabase.js\n");

/***/ }),

/***/ "(api)/../../packages/database/dist/types/supabase.js":
/*!******************************************************!*\
  !*** ../../packages/database/dist/types/supabase.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// TypeScript types for Supabase database schema\n// Auto-generated from Freela Syria database schema\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=supabase.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi4vLi4vcGFja2FnZXMvZGF0YWJhc2UvZGlzdC90eXBlcy9zdXBhYmFzZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZnJlZWxhL2xhbmRpbmctcGFnZS8uLi8uLi9wYWNrYWdlcy9kYXRhYmFzZS9kaXN0L3R5cGVzL3N1cGFiYXNlLmpzPzRmMGYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBUeXBlU2NyaXB0IHR5cGVzIGZvciBTdXBhYmFzZSBkYXRhYmFzZSBzY2hlbWFcbi8vIEF1dG8tZ2VuZXJhdGVkIGZyb20gRnJlZWxhIFN5cmlhIGRhdGFiYXNlIHNjaGVtYVxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3VwYWJhc2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/../../packages/database/dist/types/supabase.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/prisma","vendor-chunks/@prisma"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-db&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-db.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();