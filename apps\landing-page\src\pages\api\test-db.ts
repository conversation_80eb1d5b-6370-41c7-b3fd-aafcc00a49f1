import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@freela/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Testing database connection...');
    
    // Test database connection
    const userCount = await prisma.user.count();
    console.log('✅ Database connection successful, user count:', userCount);
    
    // Test if we can find any users
    const users = await prisma.user.findMany({
      take: 5,
      select: {
        id: true,
        email: true,
        role: true,
        hasCompletedOnboarding: true,
        createdAt: true
      }
    });
    
    console.log('📊 Sample users:', users);
    
    return res.status(200).json({
      success: true,
      message: 'Database connection successful',
      data: {
        userCount,
        sampleUsers: users
      }
    });
  } catch (error: any) {
    console.error('❌ Database connection failed:', error);
    return res.status(500).json({
      success: false,
      error: 'Database connection failed',
      details: error.message
    });
  }
}
