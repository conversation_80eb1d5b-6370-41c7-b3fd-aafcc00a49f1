/**
 * Experts Page - Expert Discovery and Showcase
 * Accessible after successful authentication and AI onboarding
 */

import React from 'react';
import { GetServerSideProps } from 'next';
import { getSession } from 'next-auth/react';
import { useSession } from 'next-auth/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { NextSeo } from 'next-seo';
import { useRouter } from 'next/router';

// Components
import Layout from '@/components/Layout';
import { ExpertShowcase } from '@/components/expert-showcase/ExpertShowcase';

// Types
interface Expert {
  id: string;
  name: string;
  title: string;
  avatar?: string;
  rating: number;
  reviewCount: number;
  hourlyRate: number;
  skills: string[];
  description: string;
  completedProjects: number;
  responseTime: string;
  isOnline: boolean;
  location: string;
}

const ExpertsPage: React.FC = () => {
  const { t } = useTranslation(['common', 'experts']);
  const { data: session } = useSession();
  const router = useRouter();

  const handleExpertSelect = (expert: Expert) => {
    // Navigate to expert profile or open contact modal
    console.log('Selected expert:', expert);
    // TODO: Implement expert profile view or contact functionality
    router.push(`/expert/${expert.id}`);
  };

  const userRole = (session?.user?.role as 'CLIENT' | 'EXPERT') || 'CLIENT';

  return (
    <>
      <NextSeo
        title={`${t('experts:title')} - فريلا سوريا`}
        description={t('experts:description')}
        openGraph={{
          title: `${t('experts:title')} - فريلا سوريا`,
          description: t('experts:description'),
          type: 'website',
        }}
      />

      <Layout>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
          <div className="container mx-auto px-4 py-8">
            {/* Welcome Message for Authenticated Users */}
            {session?.user && (
              <div className="mb-8 p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-lg font-bold">
                    {session.user.name?.charAt(0) || session.user.email?.charAt(0)}
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                      مرحباً، {session.user.name || 'مستخدم'}!
                    </h2>
                    <p className="text-gray-600 dark:text-gray-400">
                      {userRole === 'CLIENT' 
                        ? 'اكتشف أفضل الخبراء لمشاريعك'
                        : 'تعرف على زملائك في المنصة'
                      }
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Expert Showcase */}
            <ExpertShowcase
              userRole={userRole}
              onExpertSelect={handleExpertSelect}
              className="mb-8"
            />

            {/* Call to Action for Non-authenticated Users */}
            {!session?.user && (
              <div className="mt-12 text-center">
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 max-w-md mx-auto">
                  <div className="text-blue-500 text-5xl mb-4">🚀</div>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    انضم إلى فريلا سوريا
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    سجل الآن للوصول إلى آلاف الخبراء المحترفين أو لتقديم خدماتك
                  </p>
                  <div className="space-y-3">
                    <button
                      onClick={() => router.push('/?signup=true')}
                      className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-colors duration-200 font-medium"
                    >
                      إنشاء حساب جديد
                    </button>
                    <button
                      onClick={() => router.push('/?signin=true')}
                      className="w-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 px-6 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 font-medium"
                    >
                      تسجيل الدخول
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </Layout>
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const session = await getSession(context);
  
  return {
    props: {
      session,
      ...(await serverSideTranslations(context.locale ?? 'ar', ['common', 'experts'])),
    },
  };
};

export default ExpertsPage;
