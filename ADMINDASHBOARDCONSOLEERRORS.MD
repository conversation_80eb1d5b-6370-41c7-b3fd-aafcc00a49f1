GenAIWebpageEligibilityService.js:18 
            
            
           GET https://raw.githubusercontent.com/Bon-Appetit/porn-domains/refs/heads/master/block.txt 404 (Not Found)
fetchExplicitBlockList @ GenAIWebpageEligibilityService.js:18
getExplicitBlockList @ GenAIWebpageEligibilityService.js:18
await in getExplicitBlockList
_shouldShowTouchpoints @ GenAIWebpageEligibilityService.js:18
await in _shouldShowTouchpoints
shouldShowTouchpoints @ GenAIWebpageEligibilityService.js:18
isEligible @ ActionableCoachmark.js:18
getRenderPrompt @ ShowOneChild.js:18
await in getRenderPrompt
render @ ShowOneChild.js:18
(anonymous) @ ch-content-script-dend.js:18
await in (anonymous)
(anonymous) @ ch-content-script-dend.js:18
j @ jquery-3.1.1.min.js:2
k @ jquery-3.1.1.min.js:2
setTimeout
(anonymous) @ jquery-3.1.1.min.js:2
i @ jquery-3.1.1.min.js:2
add @ jquery-3.1.1.min.js:2
(anonymous) @ jquery-3.1.1.min.js:2
Deferred @ jquery-3.1.1.min.js:2
then @ jquery-3.1.1.min.js:2
r.fn.ready @ jquery-3.1.1.min.js:2
(anonymous) @ ch-content-script-dend.js:18
