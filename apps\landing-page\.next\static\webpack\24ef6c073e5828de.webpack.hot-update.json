{"c": ["webpack"], "r": ["/_error", "pages/index"], "m": ["../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!", "../../node_modules/@headlessui/react/dist/components/description/description.js", "../../node_modules/@headlessui/react/dist/components/dialog/dialog.js", "../../node_modules/@headlessui/react/dist/components/focus-trap/focus-trap.js", "../../node_modules/@headlessui/react/dist/components/keyboard.js", "../../node_modules/@headlessui/react/dist/components/portal/portal.js", "../../node_modules/@headlessui/react/dist/components/transitions/transition.js", "../../node_modules/@headlessui/react/dist/components/transitions/utils/transition.js", "../../node_modules/@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js", "../../node_modules/@headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js", "../../node_modules/@headlessui/react/dist/hooks/document-overflow/overflow-store.js", "../../node_modules/@headlessui/react/dist/hooks/document-overflow/prevent-scroll.js", "../../node_modules/@headlessui/react/dist/hooks/document-overflow/use-document-overflow.js", "../../node_modules/@headlessui/react/dist/hooks/use-disposables.js", "../../node_modules/@headlessui/react/dist/hooks/use-document-event.js", "../../node_modules/@headlessui/react/dist/hooks/use-event-listener.js", "../../node_modules/@headlessui/react/dist/hooks/use-event.js", "../../node_modules/@headlessui/react/dist/hooks/use-flags.js", "../../node_modules/@headlessui/react/dist/hooks/use-id.js", "../../node_modules/@headlessui/react/dist/hooks/use-inert.js", "../../node_modules/@headlessui/react/dist/hooks/use-is-mounted.js", "../../node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js", "../../node_modules/@headlessui/react/dist/hooks/use-latest-value.js", "../../node_modules/@headlessui/react/dist/hooks/use-on-unmount.js", "../../node_modules/@headlessui/react/dist/hooks/use-outside-click.js", "../../node_modules/@headlessui/react/dist/hooks/use-owner.js", "../../node_modules/@headlessui/react/dist/hooks/use-root-containers.js", "../../node_modules/@headlessui/react/dist/hooks/use-server-handoff-complete.js", "../../node_modules/@headlessui/react/dist/hooks/use-store.js", "../../node_modules/@headlessui/react/dist/hooks/use-sync-refs.js", "../../node_modules/@headlessui/react/dist/hooks/use-tab-direction.js", "../../node_modules/@headlessui/react/dist/hooks/use-transition.js", "../../node_modules/@headlessui/react/dist/hooks/use-watch.js", "../../node_modules/@headlessui/react/dist/hooks/use-window-event.js", "../../node_modules/@headlessui/react/dist/internal/hidden.js", "../../node_modules/@headlessui/react/dist/internal/open-closed.js", "../../node_modules/@headlessui/react/dist/internal/portal-force-root.js", "../../node_modules/@headlessui/react/dist/internal/stack-context.js", "../../node_modules/@headlessui/react/dist/use-sync-external-store-shim/index.js", "../../node_modules/@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimClient.js", "../../node_modules/@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimServer.js", "../../node_modules/@headlessui/react/dist/utils/active-element-history.js", "../../node_modules/@headlessui/react/dist/utils/bugs.js", "../../node_modules/@headlessui/react/dist/utils/class-names.js", "../../node_modules/@headlessui/react/dist/utils/disposables.js", "../../node_modules/@headlessui/react/dist/utils/document-ready.js", "../../node_modules/@headlessui/react/dist/utils/env.js", "../../node_modules/@headlessui/react/dist/utils/focus-management.js", "../../node_modules/@headlessui/react/dist/utils/match.js", "../../node_modules/@headlessui/react/dist/utils/micro-task.js", "../../node_modules/@headlessui/react/dist/utils/once.js", "../../node_modules/@headlessui/react/dist/utils/owner.js", "../../node_modules/@headlessui/react/dist/utils/platform.js", "../../node_modules/@headlessui/react/dist/utils/render.js", "../../node_modules/@headlessui/react/dist/utils/store.js", "../../node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/BanknotesIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js", "../../node_modules/@heroicons/react/24/outline/esm/BriefcaseIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/CheckBadgeIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/DocumentPlusIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/EyeIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/HeartIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/InboxArrowDownIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/LanguageIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/LockClosedIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/PaperAirplaneIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/PlayIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/StarIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/UserIcon.js", "../../node_modules/@heroicons/react/24/outline/esm/UserPlusIcon.js", "../../node_modules/@heroicons/react/24/solid/esm/BriefcaseIcon.js", "../../node_modules/@heroicons/react/24/solid/esm/CheckIcon.js", "../../node_modules/@heroicons/react/24/solid/esm/HeartIcon.js", "../../node_modules/@heroicons/react/24/solid/esm/PlayIcon.js", "../../node_modules/@heroicons/react/24/solid/esm/RocketLaunchIcon.js", "../../node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js", "../../node_modules/@heroicons/react/24/solid/esm/StarIcon.js", "../../node_modules/@heroicons/react/24/solid/esm/UserGroupIcon.js", "../../node_modules/@hookform/resolvers/dist/resolvers.mjs", "../../node_modules/@hookform/resolvers/zod/dist/zod.mjs", "../../node_modules/base64-js/index.js", "../../node_modules/buffer/index.js", "../../node_modules/ieee754/index.js", "../../node_modules/next-seo/lib/next-seo.module.js", "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cindex.tsx&page=%2F!", "../../node_modules/next/dist/client/components/router-reducer/router-reducer-types.js", "../../node_modules/next/dist/client/get-domain-locale.js", "../../node_modules/next/dist/client/image-component.js", "../../node_modules/next/dist/client/link.js", "../../node_modules/next/dist/client/normalize-locale-path.js", "../../node_modules/next/dist/client/use-intersection.js", "../../node_modules/next/dist/compiled/micromatch/index.js", "../../node_modules/next/dist/compiled/path-browserify/index.js", "../../node_modules/next/dist/compiled/util/util.js", "../../node_modules/next/dist/shared/lib/get-img-props.js", "../../node_modules/next/dist/shared/lib/image-blur-svg.js", "../../node_modules/next/dist/shared/lib/image-external.js", "../../node_modules/next/dist/shared/lib/image-loader.js", "../../node_modules/next/dist/shared/lib/match-remote-pattern.js", "../../node_modules/next/head.js", "../../node_modules/next/image.js", "../../node_modules/next/link.js", "../../node_modules/react-hook-form/dist/index.esm.mjs", "../../node_modules/react-intersection-observer/dist/index.mjs", "../../node_modules/zod/dist/cjs/index.js", "../../node_modules/zod/dist/cjs/v3/ZodError.js", "../../node_modules/zod/dist/cjs/v3/errors.js", "../../node_modules/zod/dist/cjs/v3/external.js", "../../node_modules/zod/dist/cjs/v3/helpers/errorUtil.js", "../../node_modules/zod/dist/cjs/v3/helpers/parseUtil.js", "../../node_modules/zod/dist/cjs/v3/helpers/typeAliases.js", "../../node_modules/zod/dist/cjs/v3/helpers/util.js", "../../node_modules/zod/dist/cjs/v3/index.js", "../../node_modules/zod/dist/cjs/v3/locales/en.js", "../../node_modules/zod/dist/cjs/v3/types.js", "../../node_modules/zod/dist/esm/index.js", "../../node_modules/zod/dist/esm/v3/ZodError.js", "../../node_modules/zod/dist/esm/v3/errors.js", "../../node_modules/zod/dist/esm/v3/external.js", "../../node_modules/zod/dist/esm/v3/helpers/errorUtil.js", "../../node_modules/zod/dist/esm/v3/helpers/parseUtil.js", "../../node_modules/zod/dist/esm/v3/helpers/typeAliases.js", "../../node_modules/zod/dist/esm/v3/helpers/util.js", "../../node_modules/zod/dist/esm/v3/index.js", "../../node_modules/zod/dist/esm/v3/locales/en.js", "../../node_modules/zod/dist/esm/v3/types.js", "../../packages/utils/dist/auth.js", "../../packages/utils/dist/constants.js", "../../packages/utils/dist/index.js", "../../packages/utils/dist/validation.js", "./src/components/Layout/Footer.tsx", "./src/components/Layout/Header.tsx", "./src/components/Layout/index.tsx", "./src/components/auth/AuthInput.tsx", "./src/components/auth/AuthModal.tsx", "./src/components/auth/GoogleAuthButton.tsx", "./src/components/auth/SignInModal.tsx", "./src/components/auth/SignUpModal.tsx", "./src/components/auth/index.ts", "./src/components/sections/About.tsx", "./src/components/sections/Contact.tsx", "./src/components/sections/Features.tsx", "./src/components/sections/Hero.tsx", "./src/components/sections/HowItWorks.tsx", "./src/components/sections/Newsletter.tsx", "./src/components/sections/Pricing.tsx", "./src/components/sections/Testimonials.tsx", "./src/components/ui/SyrianFlag.tsx", "./src/pages/index.tsx", "./src/types/auth.ts", "__barrel_optimize__?names=ArrowLeftIcon,ArrowRightIcon,BriefcaseIcon,EnvelopeIcon,LockClosedIcon,PhoneIcon,UserGroupIcon,UserIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=ArrowLeftIcon,ArrowRightIcon,EnvelopeIcon,LockClosedIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=BanknotesIcon,DocumentPlusIcon,InboxArrowDownIcon,MagnifyingGlassIcon,PaperAirplaneIcon,PlayIcon,UserIcon,UserPlusIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=Bars3Icon,BriefcaseIcon,LanguageIcon,XMarkIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=BriefcaseIcon,HeartIcon,PlayIcon,RocketLaunchIcon,SparklesIcon,StarIcon,UserGroupIcon!=!../../node_modules/@heroicons/react/24/solid/esm/index.js", "__barrel_optimize__?names=BriefcaseIcon,HeartIcon,SparklesIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckBadgeIcon,CpuChipIcon,CurrencyDollarIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,UserGroupIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,EnvelopeIcon,MapPinIcon,PaperAirplaneIcon,PhoneIcon,SparklesIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=CheckCircleIcon,ExclamationCircleIcon,EyeIcon,EyeSlashIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=CheckIcon,StarIcon!=!../../node_modules/@heroicons/react/24/solid/esm/index.js", "__barrel_optimize__?names=Dialog,Transition!=!../../node_modules/@headlessui/react/dist/headlessui.esm.js", "__barrel_optimize__?names=EnvelopeIcon,PaperAirplaneIcon,SparklesIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=HeartIcon,LightBulbIcon,StarIcon,UserGroupIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=StarIcon!=!../../node_modules/@heroicons/react/24/solid/esm/index.js", "__barrel_optimize__?names=XMarkIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js"]}