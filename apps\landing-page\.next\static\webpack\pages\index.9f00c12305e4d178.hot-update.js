"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/auth/GoogleAuthButton.tsx":
/*!**************************************************!*\
  !*** ./src/components/auth/GoogleAuthButton.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/themes */ \"./src/themes/index.tsx\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-auth/react */ \"../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst GoogleAuthButton = (param)=>{\n    let { mode, onLoading, onError, disabled = false, className = \"\" } = param;\n    _s();\n    const [isLoading, setIsLoading] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const { currentTheme, themeName } = (0,_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { locale } = router;\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"auth\");\n    const isRTL = locale === \"ar\";\n    const isGoldTheme = themeName === \"gold\";\n    const handleGoogleAuth = async ()=>{\n        if (disabled || isLoading) return;\n        try {\n            setIsLoading(true);\n            onLoading === null || onLoading === void 0 ? void 0 : onLoading(true);\n            const result = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.signIn)(\"google\", {\n                redirect: false,\n                callbackUrl: \"/ai-onboarding\"\n            });\n            if (result === null || result === void 0 ? void 0 : result.error) {\n                throw new Error(result.error);\n            }\n        // Success will be handled by the callback\n        } catch (error) {\n            // console.error('Google authentication error:', error);\n            const errorMessage = error instanceof Error ? error.message : t(\"login.networkError\");\n            onError === null || onError === void 0 ? void 0 : onError(errorMessage);\n        } finally{\n            setIsLoading(false);\n            onLoading === null || onLoading === void 0 ? void 0 : onLoading(false);\n        }\n    };\n    const buttonClasses = \"\\n    w-full h-14 px-6 rounded-xl font-semibold text-base\\n    flex items-center justify-center gap-3 group\\n    transition-all duration-300 ease-out\\n    border border-white/20 backdrop-blur-xl\\n    \".concat(isRTL ? \"font-tajawal flex-row-reverse\" : \"font-sans\", \"\\n    \").concat(disabled || isLoading ? \"opacity-50 cursor-not-allowed\" : \"hover:scale-[1.02] hover:-translate-y-1 active:scale-[0.98] cursor-pointer\", \"\\n    \").concat(className, \"\\n  \");\n    const buttonStyle = {\n        background: isGoldTheme ? \"linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%)\" : \"linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(126, 34, 206, 0.05) 100%)\",\n        color: currentTheme.colors.text.primary,\n        boxShadow: isGoldTheme ? \"0 8px 32px rgba(251, 191, 36, 0.15)\" : \"0 8px 32px rgba(147, 51, 234, 0.15)\"\n    };\n    const hoverStyle = {\n        background: isGoldTheme ? \"linear-gradient(135deg, rgba(251, 191, 36, 0.15) 0%, rgba(245, 158, 11, 0.08) 100%)\" : \"linear-gradient(135deg, rgba(147, 51, 234, 0.15) 0%, rgba(126, 34, 206, 0.08) 100%)\",\n        boxShadow: isGoldTheme ? \"0 12px 40px rgba(251, 191, 36, 0.25)\" : \"0 12px 40px rgba(147, 51, 234, 0.25)\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n        type: \"button\",\n        onClick: handleGoogleAuth,\n        disabled: disabled || isLoading,\n        className: buttonClasses,\n        style: buttonStyle,\n        whileHover: !disabled && !isLoading ? hoverStyle : {},\n        whileTap: !disabled && !isLoading ? {\n            scale: 0.98\n        } : {},\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\auth\\\\GoogleAuthButton.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: t(\"modal.loading\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\auth\\\\GoogleAuthButton.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 flex-shrink-0\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\",\n                            fill: \"#4285F4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\auth\\\\GoogleAuthButton.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\",\n                            fill: \"#34A853\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\auth\\\\GoogleAuthButton.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\",\n                            fill: \"#FBBC05\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\auth\\\\GoogleAuthButton.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\",\n                            fill: \"#EA4335\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\auth\\\\GoogleAuthButton.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\auth\\\\GoogleAuthButton.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: mode === \"signin\" ? t(\"socialLogin.google\") : t(\"socialLogin.google\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\auth\\\\GoogleAuthButton.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\auth\\\\GoogleAuthButton.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GoogleAuthButton, \"2BONvXQrS+egowb0K6k6Wh3Kv7U=\", false, function() {\n    return [\n        _themes__WEBPACK_IMPORTED_MODULE_4__.useTheme,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = GoogleAuthButton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GoogleAuthButton);\nvar _c;\n$RefreshReg$(_c, \"GoogleAuthButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/auth/GoogleAuthButton.tsx\n"));

/***/ })

});