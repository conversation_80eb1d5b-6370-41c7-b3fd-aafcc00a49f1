{
  error: Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.
      at new PrismaClient (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\default.js:43:11)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:9:46)
      at Object.<anonymous> (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:74:16)
      at Module._compile (node:internal/modules/cjs/loader:1554:14)
      at Object.transformer (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:1186)
      at Module.load (node:internal/modules/cjs/loader:1289:32)
      at Function._load (node:internal/modules/cjs/loader:1108:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
      at Module.require (node:internal/modules/cjs/loader:1311:12),
  level: 'error',
  message: 'uncaughtException: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:9:46)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:74:16)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object.transformer (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1186)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:12)',
  stack: 'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:9:46)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:74:16)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object.transformer (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1186)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:12)',
  exception: true,
  date: 'Tue Jun 10 2025 03:21:18 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 22932,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 103649280,
      heapTotal: 22339584,
      heapUsed: 15113352,
      external: 3256349,
      arrayBuffers: 174284
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 335657.296 },
  trace: [
    {
      column: 11,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js',
      function: 'new PrismaClient',
      line: 43,
      method: null,
      native: false
    },
    {
      column: 46,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts',
      function: '<anonymous>',
      line: 9,
      method: null,
      native: false
    },
    {
      column: 16,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts',
      function: null,
      line: 74,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1554,
      method: '_compile',
      native: false
    },
    {
      column: 1186,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'Object.transformer',
      line: 2,
      method: 'transformer',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1289,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1108,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 220,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 03:21:18'
}
{
  error: Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\Users\<USER>\Documents\Freela\packages\database\dist\client' imported from C:\Users\<USER>\Documents\Freela\packages\database\dist\index.js
      at finalizeResolution (node:internal/modules/esm/resolve:275:11)
      at moduleResolve (node:internal/modules/esm/resolve:860:10)
      at defaultResolve (node:internal/modules/esm/resolve:984:11)
      at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)
      at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)
      at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)
      at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)
      at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)
      at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)
      at Module._compile (node:internal/modules/cjs/loader:1536:5) {
    code: 'ERR_MODULE_NOT_FOUND',
    url: 'file:///C:/Users/<USER>/Documents/Freela/packages/database/dist/client'
  },
  level: 'error',
  message: "uncaughtException: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    "Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    '    at finalizeResolution (node:internal/modules/esm/resolve:275:11)\n' +
    '    at moduleResolve (node:internal/modules/esm/resolve:860:10)\n' +
    '    at defaultResolve (node:internal/modules/esm/resolve:984:11)\n' +
    '    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)\n' +
    '    at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)\n' +
    '    at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)\n' +
    '    at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)\n' +
    '    at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)\n' +
    '    at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1536:5)',
  stack: "Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    '    at finalizeResolution (node:internal/modules/esm/resolve:275:11)\n' +
    '    at moduleResolve (node:internal/modules/esm/resolve:860:10)\n' +
    '    at defaultResolve (node:internal/modules/esm/resolve:984:11)\n' +
    '    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)\n' +
    '    at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)\n' +
    '    at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)\n' +
    '    at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)\n' +
    '    at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)\n' +
    '    at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1536:5)',
  exception: true,
  date: 'Tue Jun 10 2025 03:30:45 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 36228,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\dist\\index.js'
    ],
    memoryUsage: {
      rss: 57012224,
      heapTotal: 18931712,
      heapUsed: 11238472,
      external: 2122639,
      arrayBuffers: 16659
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 336224.562 },
  trace: [
    {
      column: 11,
      file: 'node:internal/modules/esm/resolve',
      function: 'finalizeResolution',
      line: 275,
      method: null,
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/esm/resolve',
      function: 'moduleResolve',
      line: 860,
      method: null,
      native: false
    },
    {
      column: 11,
      file: 'node:internal/modules/esm/resolve',
      function: 'defaultResolve',
      line: 984,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.defaultResolve',
      line: 685,
      method: 'defaultResolve',
      native: false
    },
    {
      column: 25,
      file: 'node:internal/modules/esm/loader',
      function: '#cachedDefaultResolve',
      line: 634,
      method: null,
      native: false
    },
    {
      column: 53,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.getModuleJobForRequire',
      line: 384,
      method: 'getModuleJobForRequire',
      native: false
    },
    {
      column: 34,
      file: 'node:internal/modules/esm/module_job',
      function: 'new ModuleJobSync',
      line: 341,
      method: null,
      native: false
    },
    {
      column: 11,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.importSyncForRequire',
      line: 357,
      method: 'importSyncForRequire',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'loadESMFromCJS',
      line: 1385,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1536,
      method: '_compile',
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 03:30:45'
}
{
  error: Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\Users\<USER>\Documents\Freela\packages\database\dist\client' imported from C:\Users\<USER>\Documents\Freela\packages\database\dist\index.js
      at finalizeResolution (node:internal/modules/esm/resolve:275:11)
      at moduleResolve (node:internal/modules/esm/resolve:860:10)
      at defaultResolve (node:internal/modules/esm/resolve:984:11)
      at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)
      at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)
      at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)
      at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)
      at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)
      at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)
      at Module._compile (node:internal/modules/cjs/loader:1536:5) {
    code: 'ERR_MODULE_NOT_FOUND',
    url: 'file:///C:/Users/<USER>/Documents/Freela/packages/database/dist/client'
  },
  level: 'error',
  message: "uncaughtException: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    "Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    '    at finalizeResolution (node:internal/modules/esm/resolve:275:11)\n' +
    '    at moduleResolve (node:internal/modules/esm/resolve:860:10)\n' +
    '    at defaultResolve (node:internal/modules/esm/resolve:984:11)\n' +
    '    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)\n' +
    '    at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)\n' +
    '    at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)\n' +
    '    at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)\n' +
    '    at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)\n' +
    '    at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1536:5)',
  stack: "Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    '    at finalizeResolution (node:internal/modules/esm/resolve:275:11)\n' +
    '    at moduleResolve (node:internal/modules/esm/resolve:860:10)\n' +
    '    at defaultResolve (node:internal/modules/esm/resolve:984:11)\n' +
    '    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)\n' +
    '    at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)\n' +
    '    at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)\n' +
    '    at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)\n' +
    '    at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)\n' +
    '    at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1536:5)',
  exception: true,
  date: 'Tue Jun 10 2025 03:41:20 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 3904,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\dist\\index.js'
    ],
    memoryUsage: {
      rss: 56795136,
      heapTotal: 18669568,
      heapUsed: 11361624,
      external: 2122639,
      arrayBuffers: 16659
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 336858.875 },
  trace: [
    {
      column: 11,
      file: 'node:internal/modules/esm/resolve',
      function: 'finalizeResolution',
      line: 275,
      method: null,
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/esm/resolve',
      function: 'moduleResolve',
      line: 860,
      method: null,
      native: false
    },
    {
      column: 11,
      file: 'node:internal/modules/esm/resolve',
      function: 'defaultResolve',
      line: 984,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.defaultResolve',
      line: 685,
      method: 'defaultResolve',
      native: false
    },
    {
      column: 25,
      file: 'node:internal/modules/esm/loader',
      function: '#cachedDefaultResolve',
      line: 634,
      method: null,
      native: false
    },
    {
      column: 53,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.getModuleJobForRequire',
      line: 384,
      method: 'getModuleJobForRequire',
      native: false
    },
    {
      column: 34,
      file: 'node:internal/modules/esm/module_job',
      function: 'new ModuleJobSync',
      line: 341,
      method: null,
      native: false
    },
    {
      column: 11,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.importSyncForRequire',
      line: 357,
      method: 'importSyncForRequire',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'loadESMFromCJS',
      line: 1385,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1536,
      method: '_compile',
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 03:41:20'
}
{
  error: Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\Users\<USER>\Documents\Freela\packages\database\dist\client' imported from C:\Users\<USER>\Documents\Freela\packages\database\dist\index.js
      at finalizeResolution (node:internal/modules/esm/resolve:275:11)
      at moduleResolve (node:internal/modules/esm/resolve:860:10)
      at defaultResolve (node:internal/modules/esm/resolve:984:11)
      at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)
      at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)
      at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)
      at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)
      at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)
      at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)
      at Module._compile (node:internal/modules/cjs/loader:1536:5) {
    code: 'ERR_MODULE_NOT_FOUND',
    url: 'file:///C:/Users/<USER>/Documents/Freela/packages/database/dist/client'
  },
  level: 'error',
  message: "uncaughtException: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    "Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    '    at finalizeResolution (node:internal/modules/esm/resolve:275:11)\n' +
    '    at moduleResolve (node:internal/modules/esm/resolve:860:10)\n' +
    '    at defaultResolve (node:internal/modules/esm/resolve:984:11)\n' +
    '    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)\n' +
    '    at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)\n' +
    '    at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)\n' +
    '    at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)\n' +
    '    at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)\n' +
    '    at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1536:5)',
  stack: "Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    '    at finalizeResolution (node:internal/modules/esm/resolve:275:11)\n' +
    '    at moduleResolve (node:internal/modules/esm/resolve:860:10)\n' +
    '    at defaultResolve (node:internal/modules/esm/resolve:984:11)\n' +
    '    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)\n' +
    '    at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)\n' +
    '    at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)\n' +
    '    at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)\n' +
    '    at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)\n' +
    '    at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1536:5)',
  exception: true,
  date: 'Tue Jun 10 2025 03:41:28 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 38784,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\dist\\index.js'
    ],
    memoryUsage: {
      rss: 56893440,
      heapTotal: 18931712,
      heapUsed: 11265648,
      external: 2122639,
      arrayBuffers: 16659
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 336866.921 },
  trace: [
    {
      column: 11,
      file: 'node:internal/modules/esm/resolve',
      function: 'finalizeResolution',
      line: 275,
      method: null,
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/esm/resolve',
      function: 'moduleResolve',
      line: 860,
      method: null,
      native: false
    },
    {
      column: 11,
      file: 'node:internal/modules/esm/resolve',
      function: 'defaultResolve',
      line: 984,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.defaultResolve',
      line: 685,
      method: 'defaultResolve',
      native: false
    },
    {
      column: 25,
      file: 'node:internal/modules/esm/loader',
      function: '#cachedDefaultResolve',
      line: 634,
      method: null,
      native: false
    },
    {
      column: 53,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.getModuleJobForRequire',
      line: 384,
      method: 'getModuleJobForRequire',
      native: false
    },
    {
      column: 34,
      file: 'node:internal/modules/esm/module_job',
      function: 'new ModuleJobSync',
      line: 341,
      method: null,
      native: false
    },
    {
      column: 11,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.importSyncForRequire',
      line: 357,
      method: 'importSyncForRequire',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'loadESMFromCJS',
      line: 1385,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1536,
      method: '_compile',
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 03:41:28'
}
{
  error: Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.
      at new PrismaClient (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\default.js:43:11)
      at Object.<anonymous> (C:\Users\<USER>\Documents\Freela\packages\database\dist\client.js:5:39)
      at Module._compile (node:internal/modules/cjs/loader:1554:14)
      at Object..js (node:internal/modules/cjs/loader:1706:10)
      at Module.load (node:internal/modules/cjs/loader:1289:32)
      at Function._load (node:internal/modules/cjs/loader:1108:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
      at Module.require (node:internal/modules/cjs/loader:1311:12)
      at require (node:internal/modules/helpers:136:16),
  level: 'error',
  message: 'uncaughtException: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client.js:5:39)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1706:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)',
  stack: 'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client.js:5:39)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1706:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)',
  exception: true,
  date: 'Tue Jun 10 2025 04:22:54 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 24720,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\dist\\index.js'
    ],
    memoryUsage: {
      rss: 51773440,
      heapTotal: 18669568,
      heapUsed: 11303352,
      external: 2043770,
      arrayBuffers: 16659
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 339351.765 },
  trace: [
    {
      column: 11,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js',
      function: 'new PrismaClient',
      line: 43,
      method: null,
      native: false
    },
    {
      column: 39,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client.js',
      function: null,
      line: 5,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1554,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1706,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1289,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1108,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 220,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 04:22:54'
}
{
  error: Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.
      at new PrismaClient (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\default.js:43:11)
      at Object.<anonymous> (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:9:46)
      at Module._compile (node:internal/modules/cjs/loader:1554:14)
      at Object.transformer (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:1186)
      at Module.load (node:internal/modules/cjs/loader:1289:32)
      at Function._load (node:internal/modules/cjs/loader:1108:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
      at Module.require (node:internal/modules/cjs/loader:1311:12)
      at require (node:internal/modules/helpers:136:16),
  level: 'error',
  message: 'uncaughtException: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:9:46)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object.transformer (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1186)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)',
  stack: 'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:9:46)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object.transformer (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1186)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)',
  exception: true,
  date: 'Tue Jun 10 2025 04:23:25 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 36288,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 101138432,
      heapTotal: 22077440,
      heapUsed: 15070904,
      external: 3210690,
      arrayBuffers: 127433
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 339382.828 },
  trace: [
    {
      column: 11,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js',
      function: 'new PrismaClient',
      line: 43,
      method: null,
      native: false
    },
    {
      column: 46,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts',
      function: null,
      line: 9,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1554,
      method: '_compile',
      native: false
    },
    {
      column: 1186,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'Object.transformer',
      line: 2,
      method: 'transformer',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1289,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1108,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 220,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 04:23:25'
}
{
  error: Error: Transform failed with 1 error:
  C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:241:12: ERROR: Expected ";" but found "redisHealth"
      at failureErrorWithLog (C:\Users\<USER>\Documents\Freela\node_modules\esbuild\lib\main.js:1463:15)
      at C:\Users\<USER>\Documents\Freela\node_modules\esbuild\lib\main.js:734:50
      at responseCallbacks.<computed> (C:\Users\<USER>\Documents\Freela\node_modules\esbuild\lib\main.js:601:9)
      at handleIncomingPacket (C:\Users\<USER>\Documents\Freela\node_modules\esbuild\lib\main.js:656:12)
      at Socket.readFromStdout (C:\Users\<USER>\Documents\Freela\node_modules\esbuild\lib\main.js:579:7)
      at Socket.emit (node:events:518:28)
      at addChunk (node:internal/streams/readable:561:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
      at Readable.push (node:internal/streams/readable:392:5)
      at Pipe.onStreamRead (node:internal/stream_base_commons:189:23) {
    name: 'TransformError'
  },
  level: 'error',
  message: 'uncaughtException: Transform failed with 1 error:\n' +
    'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\app.ts:241:12: ERROR: Expected ";" but found "redisHealth"\n' +
    'Error: Transform failed with 1 error:\n' +
    'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\app.ts:241:12: ERROR: Expected ";" but found "redisHealth"\n' +
    '    at failureErrorWithLog (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js:1463:15)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js:734:50\n' +
    '    at responseCallbacks.<computed> (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js:601:9)\n' +
    '    at handleIncomingPacket (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js:656:12)\n' +
    '    at Socket.readFromStdout (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js:579:7)\n' +
    '    at Socket.emit (node:events:518:28)\n' +
    '    at addChunk (node:internal/streams/readable:561:12)\n' +
    '    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n' +
    '    at Readable.push (node:internal/streams/readable:392:5)\n' +
    '    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)',
  stack: 'Error: Transform failed with 1 error:\n' +
    'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\app.ts:241:12: ERROR: Expected ";" but found "redisHealth"\n' +
    '    at failureErrorWithLog (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js:1463:15)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js:734:50\n' +
    '    at responseCallbacks.<computed> (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js:601:9)\n' +
    '    at handleIncomingPacket (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js:656:12)\n' +
    '    at Socket.readFromStdout (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js:579:7)\n' +
    '    at Socket.emit (node:events:518:28)\n' +
    '    at addChunk (node:internal/streams/readable:561:12)\n' +
    '    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n' +
    '    at Readable.push (node:internal/streams/readable:392:5)\n' +
    '    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)',
  exception: true,
  date: 'Tue Jun 10 2025 05:36:20 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 31620,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 84561920,
      heapTotal: 18931712,
      heapUsed: 12957456,
      external: 3034879,
      arrayBuffers: 110024
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 343757.937 },
  trace: [
    {
      column: 15,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js',
      function: 'failureErrorWithLog',
      line: 1463,
      method: null,
      native: false
    },
    {
      column: 50,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js',
      function: null,
      line: 734,
      method: null,
      native: false
    },
    {
      column: 9,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js',
      function: 'responseCallbacks.<computed>',
      line: 601,
      method: '<computed>',
      native: false
    },
    {
      column: 12,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js',
      function: 'handleIncomingPacket',
      line: 656,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\esbuild\\lib\\main.js',
      function: 'Socket.readFromStdout',
      line: 579,
      method: 'readFromStdout',
      native: false
    },
    {
      column: 28,
      file: 'node:events',
      function: 'Socket.emit',
      line: 518,
      method: 'emit',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/streams/readable',
      function: 'addChunk',
      line: 561,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'node:internal/streams/readable',
      function: 'readableAddChunkPushByteMode',
      line: 512,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/streams/readable',
      function: 'Readable.push',
      line: 392,
      method: 'push',
      native: false
    },
    {
      column: 23,
      file: 'node:internal/stream_base_commons',
      function: 'Pipe.onStreamRead',
      line: 189,
      method: 'onStreamRead',
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:36:20'
}
{
  error: Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './src/supabase' is not defined by "exports" in C:\Users\<USER>\Documents\Freela\node_modules\@freela\database\package.json
      at exportsNotFound (node:internal/modules/esm/resolve:314:10)
      at packageExportsResolve (node:internal/modules/esm/resolve:661:9)
      at resolveExports (node:internal/modules/cjs/loader:639:36)
      at Function._findPath (node:internal/modules/cjs/loader:728:31)
      at node:internal/modules/cjs/loader:1211:27
      at nextResolveSimple (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:3:942)
      at C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:2550
      at C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:1624
      at resolveTsPaths (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:3:760)
      at C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:3:1038 {
    code: 'ERR_PACKAGE_PATH_NOT_EXPORTED'
  },
  level: 'error',
  message: `uncaughtException: Package subpath './src/supabase' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\@freela\\database\\package.json\n` +
    `Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './src/supabase' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\@freela\\database\\package.json\n` +
    '    at exportsNotFound (node:internal/modules/esm/resolve:314:10)\n' +
    '    at packageExportsResolve (node:internal/modules/esm/resolve:661:9)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:639:36)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:728:31)\n' +
    '    at node:internal/modules/cjs/loader:1211:27\n' +
    '    at nextResolveSimple (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:942)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:2550\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1624\n' +
    '    at resolveTsPaths (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:760)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:1038',
  stack: `Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './src/supabase' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\@freela\\database\\package.json\n` +
    '    at exportsNotFound (node:internal/modules/esm/resolve:314:10)\n' +
    '    at packageExportsResolve (node:internal/modules/esm/resolve:661:9)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:639:36)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:728:31)\n' +
    '    at node:internal/modules/cjs/loader:1211:27\n' +
    '    at nextResolveSimple (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:942)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:2550\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1624\n' +
    '    at resolveTsPaths (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:760)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:1038',
  exception: true,
  date: 'Sat Jun 14 2025 01:34:49 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 38324,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 160370688,
      heapTotal: 81350656,
      heapUsed: 57329512,
      external: 6466950,
      arrayBuffers: 608321
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 176572.765 },
  trace: [
    {
      column: 10,
      file: 'node:internal/modules/esm/resolve',
      function: 'exportsNotFound',
      line: 314,
      method: null,
      native: false
    },
    {
      column: 9,
      file: 'node:internal/modules/esm/resolve',
      function: 'packageExportsResolve',
      line: 661,
      method: null,
      native: false
    },
    {
      column: 36,
      file: 'node:internal/modules/cjs/loader',
      function: 'resolveExports',
      line: 639,
      method: null,
      native: false
    },
    {
      column: 31,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._findPath',
      line: 728,
      method: '_findPath',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1211,
      method: null,
      native: false
    },
    {
      column: 942,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'nextResolveSimple',
      line: 3,
      method: null,
      native: false
    },
    {
      column: 2550,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: null,
      line: 2,
      method: null,
      native: false
    },
    {
      column: 1624,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: null,
      line: 2,
      method: null,
      native: false
    },
    {
      column: 760,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'resolveTsPaths',
      line: 3,
      method: null,
      native: false
    },
    {
      column: 1038,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: null,
      line: 3,
      method: null,
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:49'
}
{
  error: Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './src/supabase' is not defined by "exports" in C:\Users\<USER>\Documents\Freela\node_modules\@freela\database\package.json
      at exportsNotFound (node:internal/modules/esm/resolve:314:10)
      at packageExportsResolve (node:internal/modules/esm/resolve:661:9)
      at resolveExports (node:internal/modules/cjs/loader:639:36)
      at Function._findPath (node:internal/modules/cjs/loader:728:31)
      at node:internal/modules/cjs/loader:1211:27
      at nextResolveSimple (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:3:942)
      at C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:2550
      at C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:1624
      at resolveTsPaths (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:3:760)
      at C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:3:1038 {
    code: 'ERR_PACKAGE_PATH_NOT_EXPORTED'
  },
  level: 'error',
  message: `uncaughtException: Package subpath './src/supabase' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\@freela\\database\\package.json\n` +
    `Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './src/supabase' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\@freela\\database\\package.json\n` +
    '    at exportsNotFound (node:internal/modules/esm/resolve:314:10)\n' +
    '    at packageExportsResolve (node:internal/modules/esm/resolve:661:9)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:639:36)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:728:31)\n' +
    '    at node:internal/modules/cjs/loader:1211:27\n' +
    '    at nextResolveSimple (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:942)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:2550\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1624\n' +
    '    at resolveTsPaths (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:760)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:1038',
  stack: `Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './src/supabase' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\@freela\\database\\package.json\n` +
    '    at exportsNotFound (node:internal/modules/esm/resolve:314:10)\n' +
    '    at packageExportsResolve (node:internal/modules/esm/resolve:661:9)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:639:36)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:728:31)\n' +
    '    at node:internal/modules/cjs/loader:1211:27\n' +
    '    at nextResolveSimple (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:942)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:2550\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1624\n' +
    '    at resolveTsPaths (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:760)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:1038',
  exception: true,
  date: 'Sat Jun 14 2025 01:34:49 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 46152,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 160477184,
      heapTotal: 81874944,
      heapUsed: 57073464,
      external: 6451957,
      arrayBuffers: 593328
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 176572.765 },
  trace: [
    {
      column: 10,
      file: 'node:internal/modules/esm/resolve',
      function: 'exportsNotFound',
      line: 314,
      method: null,
      native: false
    },
    {
      column: 9,
      file: 'node:internal/modules/esm/resolve',
      function: 'packageExportsResolve',
      line: 661,
      method: null,
      native: false
    },
    {
      column: 36,
      file: 'node:internal/modules/cjs/loader',
      function: 'resolveExports',
      line: 639,
      method: null,
      native: false
    },
    {
      column: 31,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._findPath',
      line: 728,
      method: '_findPath',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1211,
      method: null,
      native: false
    },
    {
      column: 942,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'nextResolveSimple',
      line: 3,
      method: null,
      native: false
    },
    {
      column: 2550,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: null,
      line: 2,
      method: null,
      native: false
    },
    {
      column: 1624,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: null,
      line: 2,
      method: null,
      native: false
    },
    {
      column: 760,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'resolveTsPaths',
      line: 3,
      method: null,
      native: false
    },
    {
      column: 1038,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: null,
      line: 3,
      method: null,
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:34:49'
}
{
  error: Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './src/supabase' is not defined by "exports" in C:\Users\<USER>\Documents\Freela\node_modules\@freela\database\package.json
      at exportsNotFound (node:internal/modules/esm/resolve:314:10)
      at packageExportsResolve (node:internal/modules/esm/resolve:661:9)
      at resolveExports (node:internal/modules/cjs/loader:639:36)
      at Function._findPath (node:internal/modules/cjs/loader:728:31)
      at node:internal/modules/cjs/loader:1211:27
      at nextResolveSimple (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:3:942)
      at C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:2550
      at C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:1624
      at resolveTsPaths (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:3:760)
      at C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:3:1038 {
    code: 'ERR_PACKAGE_PATH_NOT_EXPORTED'
  },
  level: 'error',
  message: `uncaughtException: Package subpath './src/supabase' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\@freela\\database\\package.json\n` +
    `Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './src/supabase' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\@freela\\database\\package.json\n` +
    '    at exportsNotFound (node:internal/modules/esm/resolve:314:10)\n' +
    '    at packageExportsResolve (node:internal/modules/esm/resolve:661:9)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:639:36)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:728:31)\n' +
    '    at node:internal/modules/cjs/loader:1211:27\n' +
    '    at nextResolveSimple (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:942)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:2550\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1624\n' +
    '    at resolveTsPaths (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:760)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:1038',
  stack: `Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './src/supabase' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\@freela\\database\\package.json\n` +
    '    at exportsNotFound (node:internal/modules/esm/resolve:314:10)\n' +
    '    at packageExportsResolve (node:internal/modules/esm/resolve:661:9)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:639:36)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:728:31)\n' +
    '    at node:internal/modules/cjs/loader:1211:27\n' +
    '    at nextResolveSimple (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:942)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:2550\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1624\n' +
    '    at resolveTsPaths (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:760)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:1038',
  exception: true,
  date: 'Sat Jun 14 2025 01:35:10 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 41940,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 148312064,
      heapTotal: 81350656,
      heapUsed: 55739480,
      external: 6303206,
      arrayBuffers: 444537
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 176593.078 },
  trace: [
    {
      column: 10,
      file: 'node:internal/modules/esm/resolve',
      function: 'exportsNotFound',
      line: 314,
      method: null,
      native: false
    },
    {
      column: 9,
      file: 'node:internal/modules/esm/resolve',
      function: 'packageExportsResolve',
      line: 661,
      method: null,
      native: false
    },
    {
      column: 36,
      file: 'node:internal/modules/cjs/loader',
      function: 'resolveExports',
      line: 639,
      method: null,
      native: false
    },
    {
      column: 31,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._findPath',
      line: 728,
      method: '_findPath',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1211,
      method: null,
      native: false
    },
    {
      column: 942,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'nextResolveSimple',
      line: 3,
      method: null,
      native: false
    },
    {
      column: 2550,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: null,
      line: 2,
      method: null,
      native: false
    },
    {
      column: 1624,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: null,
      line: 2,
      method: null,
      native: false
    },
    {
      column: 760,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'resolveTsPaths',
      line: 3,
      method: null,
      native: false
    },
    {
      column: 1038,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: null,
      line: 3,
      method: null,
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:35:10'
}
{
  error: Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './src/supabase' is not defined by "exports" in C:\Users\<USER>\Documents\Freela\node_modules\@freela\database\package.json
      at exportsNotFound (node:internal/modules/esm/resolve:314:10)
      at packageExportsResolve (node:internal/modules/esm/resolve:661:9)
      at resolveExports (node:internal/modules/cjs/loader:639:36)
      at Function._findPath (node:internal/modules/cjs/loader:728:31)
      at node:internal/modules/cjs/loader:1211:27
      at nextResolveSimple (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:3:942)
      at C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:2550
      at C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:1624
      at resolveTsPaths (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:3:760)
      at C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:3:1038 {
    code: 'ERR_PACKAGE_PATH_NOT_EXPORTED'
  },
  level: 'error',
  message: `uncaughtException: Package subpath './src/supabase' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\@freela\\database\\package.json\n` +
    `Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './src/supabase' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\@freela\\database\\package.json\n` +
    '    at exportsNotFound (node:internal/modules/esm/resolve:314:10)\n' +
    '    at packageExportsResolve (node:internal/modules/esm/resolve:661:9)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:639:36)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:728:31)\n' +
    '    at node:internal/modules/cjs/loader:1211:27\n' +
    '    at nextResolveSimple (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:942)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:2550\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1624\n' +
    '    at resolveTsPaths (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:760)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:1038',
  stack: `Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './src/supabase' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\@freela\\database\\package.json\n` +
    '    at exportsNotFound (node:internal/modules/esm/resolve:314:10)\n' +
    '    at packageExportsResolve (node:internal/modules/esm/resolve:661:9)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:639:36)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:728:31)\n' +
    '    at node:internal/modules/cjs/loader:1211:27\n' +
    '    at nextResolveSimple (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:942)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:2550\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1624\n' +
    '    at resolveTsPaths (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:760)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:3:1038',
  exception: true,
  date: 'Sat Jun 14 2025 01:37:03 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 41576,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 145973248,
      heapTotal: 81350656,
      heapUsed: 55445808,
      external: 6303206,
      arrayBuffers: 444537
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 176706.703 },
  trace: [
    {
      column: 10,
      file: 'node:internal/modules/esm/resolve',
      function: 'exportsNotFound',
      line: 314,
      method: null,
      native: false
    },
    {
      column: 9,
      file: 'node:internal/modules/esm/resolve',
      function: 'packageExportsResolve',
      line: 661,
      method: null,
      native: false
    },
    {
      column: 36,
      file: 'node:internal/modules/cjs/loader',
      function: 'resolveExports',
      line: 639,
      method: null,
      native: false
    },
    {
      column: 31,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._findPath',
      line: 728,
      method: '_findPath',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1211,
      method: null,
      native: false
    },
    {
      column: 942,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'nextResolveSimple',
      line: 3,
      method: null,
      native: false
    },
    {
      column: 2550,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: null,
      line: 2,
      method: null,
      native: false
    },
    {
      column: 1624,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: null,
      line: 2,
      method: null,
      native: false
    },
    {
      column: 760,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'resolveTsPaths',
      line: 3,
      method: null,
      native: false
    },
    {
      column: 1038,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: null,
      line: 3,
      method: null,
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-14 01:37:03'
}
