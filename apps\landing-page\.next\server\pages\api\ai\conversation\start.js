"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/ai/conversation/start";
exports.ids = ["pages/api/ai/conversation/start"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fconversation%2Fstart&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai%5Cconversation%5Cstart.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fconversation%2Fstart&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai%5Cconversation%5Cstart.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_ai_conversation_start_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\ai\\conversation\\start.ts */ \"(api)/./src/pages/api/ai/conversation/start.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_ai_conversation_start_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_ai_conversation_start_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/ai/conversation/start\",\n        pathname: \"/api/ai/conversation/start\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_ai_conversation_start_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fconversation%2Fstart&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai%5Cconversation%5Cstart.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/ai/conversation/start.ts":
/*!************************************************!*\
  !*** ./src/pages/api/ai/conversation/start.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../auth/[...nextauth] */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            success: false,\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // Get user session\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n        if (!session?.user?.id) {\n            return res.status(401).json({\n                success: false,\n                message: \"Unauthorized\"\n            });\n        }\n        const { userRole, language, sessionType, culturalContext } = req.body;\n        // Validate required fields\n        if (!userRole || !language) {\n            return res.status(400).json({\n                success: false,\n                message: \"Missing required fields: userRole, language\"\n            });\n        }\n        // Forward request to the main API\n        const apiResponse = await fetch(`${process.env.API_BASE_URL || \"http://localhost:3000\"}/api/ai/conversation/start`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": `Bearer ${session.user.id}`\n            },\n            body: JSON.stringify({\n                userRole,\n                language,\n                sessionType: sessionType || \"onboarding\",\n                culturalContext: culturalContext || {\n                    location: \"سوريا\",\n                    dialect: \"general\"\n                }\n            })\n        });\n        if (!apiResponse.ok) {\n            const errorData = await apiResponse.json().catch(()=>({}));\n            throw new Error(errorData.message || `API request failed with status ${apiResponse.status}`);\n        }\n        const apiData = await apiResponse.json();\n        if (!apiData.success) {\n            throw new Error(apiData.message || \"Failed to start conversation\");\n        }\n        // Generate welcome message based on role and language\n        const welcomeMessage = generateWelcomeMessage(userRole, language);\n        // Return successful response\n        return res.status(201).json({\n            success: true,\n            message: \"AI conversation started successfully\",\n            data: {\n                sessionId: apiData.data.sessionId,\n                currentStep: apiData.data.currentStep || \"welcome\",\n                messages: [\n                    {\n                        id: `welcome_${Date.now()}`,\n                        role: \"assistant\",\n                        content: welcomeMessage,\n                        timestamp: new Date().toISOString(),\n                        confidence: 1.0\n                    }\n                ],\n                extractedData: apiData.data.extractedData || {},\n                status: apiData.data.status || \"active\",\n                completionRate: 0.0\n            }\n        });\n    } catch (error) {\n        console.error(\"Error starting AI conversation:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"Internal server error\",\n            error: error.message\n        });\n    }\n}\n/**\n * Generate welcome message based on user role and language\n */ function generateWelcomeMessage(userRole, language) {\n    if (language === \"ar\") {\n        if (userRole === \"EXPERT\") {\n            return `مرحباً بك! أنا مساعدك الذكي في منصة فريلا سوريا. \n\nسأساعدك في إعداد ملفك المهني بطريقة تفاعلية وذكية. سنتحدث عن:\n• مهاراتك وخبراتك المهنية\n• الخدمات التي تريد تقديمها\n• أسعارك المناسبة للسوق السوري\n• كيفية جذب العملاء المناسبين\n\nلنبدأ! ما هو مجال تخصصك الرئيسي؟`;\n        } else {\n            return `مرحباً بك! أنا مساعدك الذكي في منصة فريلا سوريا.\n\nسأساعدك في تحديد احتياجاتك وإيجاد أفضل الخبراء لمشاريعك. سنتحدث عن:\n• نوع المشاريع التي تحتاج إنجازها\n• الميزانية والجدول الزمني المناسب\n• المهارات المطلوبة في الخبراء\n• كيفية التواصل الفعال مع الخبراء\n\nلنبدأ! ما نوع المشروع الذي تفكر في تنفيذه؟`;\n        }\n    } else {\n        if (userRole === \"EXPERT\") {\n            return `Welcome! I'm your AI assistant on Freela Syria platform.\n\nI'll help you set up your professional profile interactively. We'll discuss:\n• Your skills and professional experience\n• Services you want to offer\n• Appropriate pricing for the Syrian market\n• How to attract the right clients\n\nLet's start! What is your main area of expertise?`;\n        } else {\n            return `Welcome! I'm your AI assistant on Freela Syria platform.\n\nI'll help you identify your needs and find the best experts for your projects. We'll discuss:\n• Types of projects you need to complete\n• Appropriate budget and timeline\n• Required skills in experts\n• How to communicate effectively with experts\n\nLet's start! What type of project are you thinking of implementing?`;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/ai/conversation/start.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// import { PrismaAdapter } from '@next-auth/prisma-adapter';\n// import { prisma } from '@freela/database';\n// import { Prisma } from '@prisma/client';\nconst authOptions = {\n    // adapter: PrismaAdapter(prisma), // Temporarily disabled for testing\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: process.env.GOOGLE_CLIENT_ID || \"\",\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET || \"\",\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            try {\n                console.log(\"\\uD83D\\uDD10 SignIn callback triggered:\", {\n                    user: user.email,\n                    provider: account?.provider,\n                    profile: profile?.name\n                });\n                if (account?.provider === \"google\") {\n                    console.log(\"✅ Google OAuth sign-in successful for:\", user.email);\n                    // For now, we'll skip database operations and just allow sign-in\n                    // TODO: Re-enable database operations once connection is fixed\n                    return true;\n                }\n                return true;\n            } catch (error) {\n                console.error(\"❌ Error during sign in:\", error);\n                return false;\n            }\n        },\n        async jwt ({ token, user, account, profile }) {\n            console.log(\"\\uD83D\\uDD11 JWT callback triggered:\", {\n                hasUser: !!user,\n                tokenEmail: token.email,\n                userEmail: user?.email\n            });\n            if (user) {\n                console.log(\"\\uD83D\\uDC64 Setting up JWT token for new user:\", user.email);\n                // For testing, we'll set default values without database lookup\n                token.id = user.id || \"temp-id\";\n                token.role = \"CLIENT\"; // Default role\n                token.status = \"ACTIVE\";\n                token.language = \"ar\";\n                token.firstName = user.name?.split(\" \")[0] || \"User\";\n                token.lastName = user.name?.split(\" \").slice(1).join(\" \") || \"\";\n                token.avatar = user.image;\n                token.hasCompletedOnboarding = false; // Always false for new users\n                console.log(\"✅ JWT token configured:\", {\n                    id: token.id,\n                    role: token.role,\n                    hasCompletedOnboarding: token.hasCompletedOnboarding\n                });\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"\\uD83D\\uDCCB Session callback triggered:\", {\n                tokenEmail: token.email,\n                sessionEmail: session.user?.email,\n                hasCompletedOnboarding: token.hasCompletedOnboarding\n            });\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.language = token.language;\n                session.user.firstName = token.firstName;\n                session.user.lastName = token.lastName;\n                session.user.avatar = token.avatar;\n                session.user.hasCompletedOnboarding = token.hasCompletedOnboarding;\n                console.log(\"✅ Session configured:\", {\n                    id: session.user.id,\n                    role: session.user.role,\n                    hasCompletedOnboarding: session.user.hasCompletedOnboarding\n                });\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Handle role-based redirects after successful authentication\n            try {\n                console.log(\"\\uD83D\\uDD04 NextAuth redirect called with:\", {\n                    url,\n                    baseUrl\n                });\n                // CRITICAL: Always redirect to AI onboarding for new users\n                // This ensures all authenticated users go through the AI-powered profile setup\n                console.log(\"\\uD83E\\uDD16 Forcing redirect to AI onboarding for all authenticated users\");\n                return `${baseUrl}/ai-onboarding`;\n            } catch (error) {\n                console.error(\"❌ Redirect error:\", error);\n                return `${baseUrl}/?auth=error`;\n            }\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    events: {\n        async signIn ({ user, account }) {\n            console.log(`🎉 User ${user.email} signed in with ${account?.provider}`);\n        // TODO: Re-enable database operations once connection is fixed\n        },\n        async signOut ({ token }) {\n            console.log(`👋 User ${token?.email} signed out`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fconversation%2Fstart&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai%5Cconversation%5Cstart.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();