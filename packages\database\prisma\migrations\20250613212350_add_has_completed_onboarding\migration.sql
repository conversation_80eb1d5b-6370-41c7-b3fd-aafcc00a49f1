-- AlterTable
ALTER TABLE "users" ADD COLUMN     "hasCompletedOnboarding" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "provider" TEXT,
ADD COLUMN     "providerId" TEXT;

-- CreateTable
CREATE TABLE "ai_conversation_sessions" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "sessionType" TEXT NOT NULL,
    "userRole" "UserRole" NOT NULL,
    "language" TEXT NOT NULL DEFAULT 'ar',
    "currentStep" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "aiModel" TEXT NOT NULL DEFAULT 'openai/gpt-4-turbo-preview',
    "temperature" DOUBLE PRECISION NOT NULL DEFAULT 0.7,
    "maxTokens" INTEGER NOT NULL DEFAULT 1000,
    "completionRate" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "stepsCompleted" TEXT[],
    "totalSteps" INTEGER NOT NULL DEFAULT 0,
    "extractedData" JSONB NOT NULL DEFAULT '{}',
    "profileData" JSONB,
    "serviceData" JSONB,
    "recommendations" JSONB[],
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastActiveAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "estimatedDuration" INTEGER,
    "actualDuration" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_conversation_sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ai_conversation_messages" (
    "id" TEXT NOT NULL,
    "sessionId" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "contentArabic" TEXT,
    "messageType" TEXT NOT NULL DEFAULT 'text',
    "stepName" TEXT,
    "intent" TEXT,
    "confidence" DOUBLE PRECISION,
    "aiModel" TEXT,
    "promptTokens" INTEGER,
    "completionTokens" INTEGER,
    "totalTokens" INTEGER,
    "processingTime" INTEGER,
    "userFeedback" TEXT,
    "userRating" INTEGER,
    "flagged" BOOLEAN NOT NULL DEFAULT false,
    "flagReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_conversation_messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ai_extracted_data" (
    "id" TEXT NOT NULL,
    "sessionId" TEXT NOT NULL,
    "dataType" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "subcategory" TEXT,
    "originalText" TEXT NOT NULL,
    "extractedValue" JSONB NOT NULL,
    "normalizedValue" TEXT,
    "confidence" DOUBLE PRECISION NOT NULL,
    "validated" BOOLEAN NOT NULL DEFAULT false,
    "validatedBy" TEXT,
    "validatedAt" TIMESTAMP(3),
    "validationNotes" TEXT,
    "extractionMethod" TEXT NOT NULL,
    "aiModel" TEXT,
    "processingVersion" TEXT NOT NULL DEFAULT '1.0',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_extracted_data_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ai_recommendations" (
    "id" TEXT NOT NULL,
    "sessionId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "title" TEXT NOT NULL,
    "titleArabic" TEXT,
    "description" TEXT NOT NULL,
    "descriptionArabic" TEXT,
    "recommendationData" JSONB NOT NULL,
    "actionRequired" JSONB,
    "expectedImpact" TEXT,
    "confidenceScore" DOUBLE PRECISION NOT NULL,
    "marketAnalysis" JSONB,
    "competitorAnalysis" JSONB,
    "successProbability" DOUBLE PRECISION,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "userFeedback" TEXT,
    "userRating" INTEGER,
    "implementedAt" TIMESTAMP(3),
    "viewedAt" TIMESTAMP(3),
    "respondedAt" TIMESTAMP(3),
    "expiresAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_recommendations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ai_market_intelligence" (
    "id" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "subcategory" TEXT,
    "region" TEXT NOT NULL DEFAULT 'syria',
    "averagePrice" DOUBLE PRECISION,
    "priceRange" JSONB,
    "pricingModel" TEXT,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "demandLevel" TEXT,
    "demandTrend" TEXT,
    "seasonality" JSONB,
    "competitorCount" INTEGER,
    "competitionLevel" TEXT,
    "topSkills" TEXT[],
    "opportunities" JSONB[],
    "threats" JSONB[],
    "recommendations" JSONB[],
    "dataSource" TEXT NOT NULL,
    "dataQuality" DOUBLE PRECISION NOT NULL DEFAULT 0.8,
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "validUntil" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_market_intelligence_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ai_conversation_sessions_userId_idx" ON "ai_conversation_sessions"("userId");

-- CreateIndex
CREATE INDEX "ai_conversation_sessions_status_idx" ON "ai_conversation_sessions"("status");

-- CreateIndex
CREATE INDEX "ai_conversation_sessions_sessionType_idx" ON "ai_conversation_sessions"("sessionType");

-- CreateIndex
CREATE INDEX "ai_conversation_messages_sessionId_idx" ON "ai_conversation_messages"("sessionId");

-- CreateIndex
CREATE INDEX "ai_conversation_messages_role_idx" ON "ai_conversation_messages"("role");

-- CreateIndex
CREATE INDEX "ai_conversation_messages_createdAt_idx" ON "ai_conversation_messages"("createdAt");

-- CreateIndex
CREATE INDEX "ai_extracted_data_sessionId_idx" ON "ai_extracted_data"("sessionId");

-- CreateIndex
CREATE INDEX "ai_extracted_data_dataType_idx" ON "ai_extracted_data"("dataType");

-- CreateIndex
CREATE INDEX "ai_extracted_data_validated_idx" ON "ai_extracted_data"("validated");

-- CreateIndex
CREATE INDEX "ai_recommendations_userId_idx" ON "ai_recommendations"("userId");

-- CreateIndex
CREATE INDEX "ai_recommendations_type_idx" ON "ai_recommendations"("type");

-- CreateIndex
CREATE INDEX "ai_recommendations_status_idx" ON "ai_recommendations"("status");

-- CreateIndex
CREATE INDEX "ai_recommendations_priority_idx" ON "ai_recommendations"("priority");

-- CreateIndex
CREATE INDEX "ai_market_intelligence_category_idx" ON "ai_market_intelligence"("category");

-- CreateIndex
CREATE INDEX "ai_market_intelligence_region_idx" ON "ai_market_intelligence"("region");

-- CreateIndex
CREATE INDEX "ai_market_intelligence_lastUpdated_idx" ON "ai_market_intelligence"("lastUpdated");

-- AddForeignKey
ALTER TABLE "ai_conversation_sessions" ADD CONSTRAINT "ai_conversation_sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_conversation_messages" ADD CONSTRAINT "ai_conversation_messages_sessionId_fkey" FOREIGN KEY ("sessionId") REFERENCES "ai_conversation_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_extracted_data" ADD CONSTRAINT "ai_extracted_data_sessionId_fkey" FOREIGN KEY ("sessionId") REFERENCES "ai_conversation_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_recommendations" ADD CONSTRAINT "ai_recommendations_sessionId_fkey" FOREIGN KEY ("sessionId") REFERENCES "ai_conversation_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_recommendations" ADD CONSTRAINT "ai_recommendations_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
