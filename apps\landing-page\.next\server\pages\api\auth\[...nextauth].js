"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\[...nextauth].ts */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// import { PrismaAdapter } from '@next-auth/prisma-adapter';\n// import { prisma } from '@freela/database';\n// import { Prisma } from '@prisma/client';\nconst authOptions = {\n    // adapter: PrismaAdapter(prisma), // Temporarily disabled for testing\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: process.env.GOOGLE_CLIENT_ID || \"\",\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET || \"\",\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            try {\n                console.log(\"\\uD83D\\uDD10 SignIn callback triggered:\", {\n                    user: user.email,\n                    provider: account?.provider,\n                    profile: profile?.name\n                });\n                if (account?.provider === \"google\") {\n                    console.log(\"✅ Google OAuth sign-in successful for:\", user.email);\n                    // For now, we'll skip database operations and just allow sign-in\n                    // TODO: Re-enable database operations once connection is fixed\n                    return true;\n                }\n                return true;\n            } catch (error) {\n                console.error(\"❌ Error during sign in:\", error);\n                return false;\n            }\n        },\n        async jwt ({ token, user, account, profile }) {\n            console.log(\"\\uD83D\\uDD11 JWT callback triggered:\", {\n                hasUser: !!user,\n                tokenEmail: token.email,\n                userEmail: user?.email\n            });\n            if (user) {\n                console.log(\"\\uD83D\\uDC64 Setting up JWT token for new user:\", user.email);\n                // For testing, we'll set default values without database lookup\n                token.id = user.id || \"temp-id\";\n                token.role = \"CLIENT\"; // Default role\n                token.status = \"ACTIVE\";\n                token.language = \"ar\";\n                token.firstName = user.name?.split(\" \")[0] || \"User\";\n                token.lastName = user.name?.split(\" \").slice(1).join(\" \") || \"\";\n                token.avatar = user.image;\n                token.hasCompletedOnboarding = false; // Always false for new users\n                console.log(\"✅ JWT token configured:\", {\n                    id: token.id,\n                    role: token.role,\n                    hasCompletedOnboarding: token.hasCompletedOnboarding\n                });\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"\\uD83D\\uDCCB Session callback triggered:\", {\n                tokenEmail: token.email,\n                sessionEmail: session.user?.email,\n                hasCompletedOnboarding: token.hasCompletedOnboarding\n            });\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.language = token.language;\n                session.user.firstName = token.firstName;\n                session.user.lastName = token.lastName;\n                session.user.avatar = token.avatar;\n                session.user.hasCompletedOnboarding = token.hasCompletedOnboarding;\n                console.log(\"✅ Session configured:\", {\n                    id: session.user.id,\n                    role: session.user.role,\n                    hasCompletedOnboarding: session.user.hasCompletedOnboarding\n                });\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Handle role-based redirects after successful authentication\n            try {\n                console.log(\"\\uD83D\\uDD04 NextAuth redirect called with:\", {\n                    url,\n                    baseUrl\n                });\n                // CRITICAL: Always redirect to AI onboarding for new users\n                // This ensures all authenticated users go through the AI-powered profile setup\n                console.log(\"\\uD83E\\uDD16 Forcing redirect to AI onboarding for all authenticated users\");\n                return `${baseUrl}/ai-onboarding`;\n            } catch (error) {\n                console.error(\"❌ Redirect error:\", error);\n                return `${baseUrl}/?auth=error`;\n            }\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    events: {\n        async signIn ({ user, account }) {\n            console.log(`🎉 User ${user.email} signed in with ${account?.provider}`);\n        // TODO: Re-enable database operations once connection is fixed\n        },\n        async signOut ({ token }) {\n            console.log(`👋 User ${token?.email} signed out`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();