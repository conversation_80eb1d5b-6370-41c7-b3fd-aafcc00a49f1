/**
 * Expert Showcase Component
 * Displays available experts after successful authentication and AI onboarding
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import { motion } from 'framer-motion';
import Image from 'next/image';

// Types
interface Expert {
  id: string;
  name: string;
  title: string;
  avatar?: string;
  rating: number;
  reviewCount: number;
  hourlyRate: number;
  skills: string[];
  description: string;
  completedProjects: number;
  responseTime: string;
  isOnline: boolean;
  location: string;
}

interface ExpertShowcaseProps {
  userRole: 'CLIENT' | 'EXPERT';
  onExpertSelect?: (expert: Expert) => void;
  className?: string;
}

export const ExpertShowcase: React.FC<ExpertShowcaseProps> = ({
  userRole,
  onExpertSelect,
  className = ''
}) => {
  const { t } = useTranslation(['common', 'experts']);
  const [experts, setExperts] = useState<Expert[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setExperts([
        {
          id: '1',
          name: 'أحمد محمد',
          title: 'مطور تطبيقات الهاتف المحمول',
          rating: 4.9,
          reviewCount: 127,
          hourlyRate: 25,
          skills: ['React Native', 'Flutter', 'iOS', 'Android'],
          description: 'مطور محترف مع خبرة 5 سنوات في تطوير تطبيقات الهاتف المحمول',
          completedProjects: 89,
          responseTime: 'خلال ساعة',
          isOnline: true,
          location: 'دمشق، سوريا'
        },
        {
          id: '2',
          name: 'فاطمة أحمد',
          title: 'مصممة جرافيك ومواقع',
          rating: 4.8,
          reviewCount: 95,
          hourlyRate: 20,
          skills: ['Photoshop', 'Illustrator', 'Figma', 'UI/UX'],
          description: 'مصممة إبداعية متخصصة في تصميم الهوية البصرية والمواقع',
          completedProjects: 156,
          responseTime: 'خلال 30 دقيقة',
          isOnline: true,
          location: 'حلب، سوريا'
        },
        {
          id: '3',
          name: 'محمد علي',
          title: 'مطور مواقع ويب',
          rating: 4.7,
          reviewCount: 203,
          hourlyRate: 30,
          skills: ['React', 'Node.js', 'Python', 'AWS'],
          description: 'مطور full-stack مع خبرة في بناء تطبيقات ويب متقدمة',
          completedProjects: 78,
          responseTime: 'خلال ساعتين',
          isOnline: false,
          location: 'اللاذقية، سوريا'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const categories = [
    { id: 'all', name: 'جميع التخصصات' },
    { id: 'development', name: 'البرمجة والتطوير' },
    { id: 'design', name: 'التصميم' },
    { id: 'marketing', name: 'التسويق' },
    { id: 'writing', name: 'الكتابة والترجمة' }
  ];

  const filteredExperts = experts.filter(expert => {
    const matchesSearch = expert.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         expert.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         expert.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()));
    
    if (selectedCategory === 'all') return matchesSearch;
    
    // Simple category filtering based on skills/title
    const categoryMap: { [key: string]: string[] } = {
      development: ['React', 'Node.js', 'Python', 'React Native', 'Flutter'],
      design: ['Photoshop', 'Illustrator', 'Figma', 'UI/UX'],
      marketing: ['SEO', 'Social Media', 'Google Ads'],
      writing: ['Content Writing', 'Translation', 'Copywriting']
    };
    
    const categorySkills = categoryMap[selectedCategory] || [];
    const matchesCategory = expert.skills.some(skill => 
      categorySkills.some(catSkill => skill.includes(catSkill))
    );
    
    return matchesSearch && matchesCategory;
  });

  if (loading) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">جاري تحميل الخبراء...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          {userRole === 'CLIENT' ? 'اكتشف الخبراء' : 'الخبراء في المنصة'}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {userRole === 'CLIENT' 
            ? 'تصفح مجموعة من أفضل الخبراء السوريين في مختلف المجالات'
            : 'تعرف على زملائك الخبراء في المنصة'
          }
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8 space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <input
            type="text"
            placeholder="ابحث عن خبير أو مهارة..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full px-4 py-3 pl-12 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* Category Filters */}
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Experts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredExperts.map((expert, index) => (
          <motion.div
            key={expert.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden"
          >
            {/* Expert Card Header */}
            <div className="p-6">
              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                {/* Avatar */}
                <div className="relative">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xl font-bold">
                    {expert.name.charAt(0)}
                  </div>
                  {expert.isOnline && (
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 border-2 border-white rounded-full"></div>
                  )}
                </div>

                {/* Expert Info */}
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                    {expert.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    {expert.title}
                  </p>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <div className="flex items-center">
                      <span className="text-yellow-400">★</span>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mr-1">
                        {expert.rating}
                      </span>
                      <span className="text-sm text-gray-500">({expert.reviewCount})</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-4 mb-4">
                {expert.description}
              </p>

              {/* Skills */}
              <div className="flex flex-wrap gap-2 mb-4">
                {expert.skills.slice(0, 3).map((skill) => (
                  <span
                    key={skill}
                    className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full"
                  >
                    {skill}
                  </span>
                ))}
                {expert.skills.length > 3 && (
                  <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-full">
                    +{expert.skills.length - 3}
                  </span>
                )}
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4">
                <div>
                  <span className="font-medium">{expert.completedProjects}</span> مشروع مكتمل
                </div>
                <div>
                  <span className="font-medium">${expert.hourlyRate}</span> / ساعة
                </div>
              </div>

              {/* Action Button */}
              {userRole === 'CLIENT' && (
                <button
                  onClick={() => onExpertSelect?.(expert)}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-2 px-4 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-colors duration-200 font-medium"
                >
                  عرض الملف الشخصي
                </button>
              )}
              
              {userRole === 'EXPERT' && (
                <button className="w-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 font-medium">
                  إرسال رسالة
                </button>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {/* No Results */}
      {filteredExperts.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            لم يتم العثور على خبراء
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            جرب تغيير معايير البحث أو التصفية
          </p>
        </div>
      )}
    </div>
  );
};

export default ExpertShowcase;
